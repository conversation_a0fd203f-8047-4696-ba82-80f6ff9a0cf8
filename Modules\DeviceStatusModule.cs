using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Threading;
using MainDispaly.Models;
using MainDispaly.Services;
using MainDispaly.ViewModels;

namespace MainDispaly.Modules
{
    /// <summary>
    /// 设备状态监控模块
    /// </summary>
    public class DeviceStatusModule : BaseViewModel
    {
        private readonly DataService _dataService;
        private DeviceStatus _deviceStatus;
        private DispatcherTimer _monitoringTimer;
        private bool _isMonitoring;
        private string _overallStatusText;

        /// <summary>
        /// 设备状态
        /// </summary>
        public DeviceStatus DeviceStatus
        {
            get => _deviceStatus;
            set => SetProperty(ref _deviceStatus, value);
        }

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring
        {
            get => _isMonitoring;
            set => SetProperty(ref _isMonitoring, value);
        }

        /// <summary>
        /// 整体状态文本
        /// </summary>
        public string OverallStatusText
        {
            get => _overallStatusText;
            set => SetProperty(ref _overallStatusText, value);
        }

        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        public event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;

        /// <summary>
        /// 环境数据更新事件
        /// </summary>
        public event EventHandler<EnvironmentDataUpdatedEventArgs> EnvironmentDataUpdated;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataService">数据服务</param>
        public DeviceStatusModule(DataService dataService)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            
            InitializeDeviceStatus();
            InitializeTimer();
            
            LogMessage("设备状态模块初始化完成");
        }

        /// <summary>
        /// 初始化设备状态
        /// </summary>
        private void InitializeDeviceStatus()
        {
            DeviceStatus = new DeviceStatus();
            DeviceStatus.PropertyChanged += DeviceStatus_PropertyChanged;
            
            // 监听系统组件状态变化
            foreach (var component in DeviceStatus.SystemComponents)
            {
                component.PropertyChanged += Component_PropertyChanged;
            }
            
            UpdateOverallStatus();
        }

        /// <summary>
        /// 初始化监控定时器
        /// </summary>
        private void InitializeTimer()
        {
            _monitoringTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2) // 每2秒更新一次
            };
            _monitoringTimer.Tick += MonitoringTimer_Tick;
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public async Task<bool> StartMonitoringAsync()
        {
            try
            {
                LogMessage("开始设备状态监控");
                
                IsMonitoring = true;
                _monitoringTimer.Start();
                
                // 初始化环境数据
                await UpdateEnvironmentDataAsync();
                
                LogMessage("设备状态监控已启动");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"启动设备监控失败: {ex.Message}", LogLevel.Error);
                IsMonitoring = false;
                return false;
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public async Task StopMonitoringAsync()
        {
            try
            {
                LogMessage("停止设备状态监控");
                
                _monitoringTimer.Stop();
                IsMonitoring = false;
                
                LogMessage("设备状态监控已停止");
            }
            catch (Exception ex)
            {
                LogMessage($"停止设备监控失败: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 更新组件状态
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <param name="status">新状态</param>
        /// <param name="description">状态描述</param>
        public void UpdateComponentStatus(string componentName, ComponentStatus status, string description = null)
        {
            try
            {
                DeviceStatus.UpdateComponentStatus(componentName, status, description);
                UpdateOverallStatus();
                
                var component = DeviceStatus.SystemComponents.FirstOrDefault(c => c.Name == componentName);
                if (component != null)
                {
                    OnDeviceStatusChanged(new DeviceStatusChangedEventArgs(component));
                    LogMessage($"组件 {componentName} 状态更新: {status}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新组件状态失败: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 更新环境数据
        /// </summary>
        public async Task UpdateEnvironmentDataAsync()
        {
            try
            {
                // 模拟从传感器读取环境数据
                DeviceStatus.EnvironmentData.GenerateRandomData();
                
                // 保存环境数据
                await _dataService.SaveEnvironmentDataAsync(DeviceStatus.EnvironmentData);
                
                OnEnvironmentDataUpdated(new EnvironmentDataUpdatedEventArgs(DeviceStatus.EnvironmentData));
                
                // 检查环境状态
                CheckEnvironmentStatus();
            }
            catch (Exception ex)
            {
                LogMessage($"更新环境数据失败: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 检查环境状态
        /// </summary>
        private void CheckEnvironmentStatus()
        {
            var envData = DeviceStatus.EnvironmentData;
            
            // 检查温度
            if (envData.TemperatureStatus == EnvironmentStatus.Warning)
            {
                LogMessage($"温度警告: {envData.Temperature:F1}°C", LogLevel.Warning);
            }
            else if (envData.TemperatureStatus == EnvironmentStatus.Error)
            {
                LogMessage($"温度异常: {envData.Temperature:F1}°C", LogLevel.Error);
            }
            
            // 检查湿度
            if (envData.HumidityStatus == EnvironmentStatus.Warning)
            {
                LogMessage($"湿度警告: {envData.Humidity:F1}%", LogLevel.Warning);
            }
            else if (envData.HumidityStatus == EnvironmentStatus.Error)
            {
                LogMessage($"湿度异常: {envData.Humidity:F1}%", LogLevel.Error);
            }
            
            // 检查电压
            if (envData.VoltageStatus == EnvironmentStatus.Warning)
            {
                LogMessage($"电压警告: {envData.Voltage:F1}V", LogLevel.Warning);
            }
            else if (envData.VoltageStatus == EnvironmentStatus.Error)
            {
                LogMessage($"电压异常: {envData.Voltage:F1}V", LogLevel.Error);
            }
        }

        /// <summary>
        /// 执行系统诊断
        /// </summary>
        public async Task<bool> RunSystemDiagnosticsAsync()
        {
            try
            {
                LogMessage("开始系统诊断");
                
                bool allSystemsOk = true;
                
                // 检查每个系统组件
                foreach (var component in DeviceStatus.SystemComponents)
                {
                    LogMessage($"诊断组件: {component.Name}");
                    
                    // 模拟诊断过程
                    await Task.Delay(500);
                    
                    // 随机生成诊断结果
                    var random = new Random();
                    var diagnosticResult = random.NextDouble();
                    
                    if (diagnosticResult > 0.9) // 10% 概率出现错误
                    {
                        component.Status = ComponentStatus.Error;
                        component.Description = $"{component.Name}诊断发现错误";
                        allSystemsOk = false;
                        LogMessage($"组件 {component.Name} 诊断失败", LogLevel.Error);
                    }
                    else if (diagnosticResult > 0.7) // 20% 概率出现警告
                    {
                        component.Status = ComponentStatus.Warning;
                        component.Description = $"{component.Name}诊断发现警告";
                        LogMessage($"组件 {component.Name} 诊断警告", LogLevel.Warning);
                    }
                    else
                    {
                        component.Status = ComponentStatus.Normal;
                        component.Description = $"{component.Name}诊断正常";
                        LogMessage($"组件 {component.Name} 诊断正常");
                    }
                }
                
                UpdateOverallStatus();
                LogMessage($"系统诊断完成，结果: {(allSystemsOk ? "正常" : "发现问题")}");
                
                return allSystemsOk;
            }
            catch (Exception ex)
            {
                LogMessage($"系统诊断失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 重置组件状态
        /// </summary>
        /// <param name="componentName">组件名称</param>
        public async Task<bool> ResetComponentAsync(string componentName)
        {
            try
            {
                LogMessage($"重置组件: {componentName}");
                
                var component = DeviceStatus.SystemComponents.FirstOrDefault(c => c.Name == componentName);
                if (component == null)
                    return false;
                
                // 模拟重置过程
                component.Status = ComponentStatus.Unknown;
                component.Description = "正在重置...";
                
                await Task.Delay(2000);
                
                // 重置完成
                component.Status = ComponentStatus.Normal;
                component.Description = $"{componentName}重置完成";
                
                UpdateOverallStatus();
                LogMessage($"组件 {componentName} 重置完成");
                
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"重置组件失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 更新整体状态
        /// </summary>
        private void UpdateOverallStatus()
        {
            var overallStatus = DeviceStatus.OverallStatus;
            switch (overallStatus)
            {
                case ComponentStatus.Normal:
                    OverallStatusText = "所有系统正常运行";
                    break;
                case ComponentStatus.Warning:
                    OverallStatusText = "系统存在警告";
                    break;
                case ComponentStatus.Error:
                    OverallStatusText = "系统存在错误";
                    break;
                default:
                    OverallStatusText = "系统状态未知";
                    break;
            }
        }

        /// <summary>
        /// 监控定时器事件
        /// </summary>
        private async void MonitoringTimer_Tick(object sender, EventArgs e)
        {
            if (IsMonitoring)
            {
                await UpdateEnvironmentDataAsync();
            }
        }

        /// <summary>
        /// 设备状态属性变化事件
        /// </summary>
        private void DeviceStatus_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(DeviceStatus.OverallStatus))
            {
                UpdateOverallStatus();
            }
        }

        /// <summary>
        /// 组件属性变化事件
        /// </summary>
        private void Component_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (sender is SystemComponent component && e.PropertyName == nameof(SystemComponent.Status))
            {
                OnDeviceStatusChanged(new DeviceStatusChangedEventArgs(component));
            }
        }

        /// <summary>
        /// 触发设备状态变化事件
        /// </summary>
        private void OnDeviceStatusChanged(DeviceStatusChangedEventArgs e)
        {
            DeviceStatusChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发环境数据更新事件
        /// </summary>
        private void OnEnvironmentDataUpdated(EnvironmentDataUpdatedEventArgs e)
        {
            EnvironmentDataUpdated?.Invoke(this, e);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _monitoringTimer?.Stop();
            
            if (DeviceStatus != null)
            {
                DeviceStatus.PropertyChanged -= DeviceStatus_PropertyChanged;
                
                foreach (var component in DeviceStatus.SystemComponents)
                {
                    component.PropertyChanged -= Component_PropertyChanged;
                }
            }
            
            LogMessage("设备状态模块资源已释放");
        }
    }

    /// <summary>
    /// 设备状态变化事件参数
    /// </summary>
    public class DeviceStatusChangedEventArgs : EventArgs
    {
        public SystemComponent Component { get; private set; }

        public DeviceStatusChangedEventArgs(SystemComponent component)
        {
            Component = component;
        }
    }

    /// <summary>
    /// 环境数据更新事件参数
    /// </summary>
    public class EnvironmentDataUpdatedEventArgs : EventArgs
    {
        public EnvironmentData EnvironmentData { get; private set; }

        public EnvironmentDataUpdatedEventArgs(EnvironmentData environmentData)
        {
            EnvironmentData = environmentData;
        }
    }
}
