{"version": 3, "targets": {".NETFramework,Version=v4.5.2": {"Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}}, ".NETFramework,Version=v4.5.2/win": {"Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}}, ".NETFramework,Version=v4.5.2/win-arm64": {"Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}}, ".NETFramework,Version=v4.5.2/win-x64": {"Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}}, ".NETFramework,Version=v4.5.2/win-x86": {"Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}}}, "libraries": {"Newtonsoft.Json/12.0.3": {"sha512": "6mgjfnRB4jKMlzHSl+VD+oUc1IebOZabkbyWj2RiTgWwYPPuaK1H97G1sHqGwPlS5npiF5Q0OrxN1wni2n5QWg==", "type": "package", "path": "newtonsoft.json/12.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.12.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.5.2": ["Newtonsoft.Json >= 12.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\MainDispaly.csproj", "projectName": "MainDispaly", "projectPath": "C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\MainDispaly.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net452"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net452": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net452": {"dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[12.0.3, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "包 \"Newtonsoft.Json\" 12.0.3 具有已知的 高 严重性漏洞，https://github.com/advisories/GHSA-5crp-9r3c-p9vr", "libraryId": "Newtonsoft.Json", "targetGraphs": [".NETFramework,Version=v4.5.2", ".NETFramework,Version=v4.5.2/win", ".NETFramework,Version=v4.5.2/win-arm64", ".NETFramework,Version=v4.5.2/win-x64", ".NETFramework,Version=v4.5.2/win-x86"]}]}