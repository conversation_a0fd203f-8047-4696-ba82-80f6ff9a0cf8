using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using MainDispaly.Models;

namespace MainDispaly.Services
{
    /// <summary>
    /// 数据服务类，负责数据的读取、保存和管理
    /// </summary>
    public class DataService
    {
        private readonly string _dataDirectory;
        private readonly List<EnvironmentData> _environmentHistory;
        private readonly List<ExperimentData> _experimentHistory;

        /// <summary>
        /// 构造函数
        /// </summary>
        public DataService()
        {
            _dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "MainDisplay", "Data");
            _environmentHistory = new List<EnvironmentData>();
            _experimentHistory = new List<ExperimentData>();

            EnsureDataDirectoryExists();
        }

        /// <summary>
        /// 确保数据目录存在
        /// </summary>
        private void EnsureDataDirectoryExists()
        {
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
            }
        }

        /// <summary>
        /// 保存实验数据
        /// </summary>
        /// <param name="experimentData">实验数据</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveExperimentDataAsync(ExperimentData experimentData)
        {
            try
            {
                var fileName = $"Experiment_{experimentData.ExperimentNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(_dataDirectory, fileName);

                var jsonData = System.Text.Json.JsonSerializer.Serialize(experimentData, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                await File.WriteAllTextAsync(filePath, jsonData);
                _experimentHistory.Add(experimentData);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存实验数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存环境数据
        /// </summary>
        /// <param name="environmentData">环境数据</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveEnvironmentDataAsync(EnvironmentData environmentData)
        {
            try
            {
                _environmentHistory.Add(environmentData);

                // 每100条记录保存一次到文件
                if (_environmentHistory.Count % 100 == 0)
                {
                    var fileName = $"Environment_{DateTime.Now:yyyyMMdd}.json";
                    var filePath = Path.Combine(_dataDirectory, fileName);

                    var jsonData = System.Text.Json.JsonSerializer.Serialize(_environmentHistory, new System.Text.Json.JsonSerializerOptions
                    {
                        WriteIndented = true
                    });

                    await File.WriteAllTextAsync(filePath, jsonData);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存环境数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载实验数据
        /// </summary>
        /// <param name="experimentNumber">实验编号</param>
        /// <returns>实验数据</returns>
        public async Task<ExperimentData> LoadExperimentDataAsync(string experimentNumber)
        {
            try
            {
                var files = Directory.GetFiles(_dataDirectory, $"Experiment_{experimentNumber}_*.json");
                if (files.Length == 0)
                    return null;

                var latestFile = files[files.Length - 1]; // 获取最新的文件
                var jsonData = await File.ReadAllTextAsync(latestFile);
                
                return System.Text.Json.JsonSerializer.Deserialize<ExperimentData>(jsonData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载实验数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取环境数据历史
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>环境数据列表</returns>
        public List<EnvironmentData> GetEnvironmentHistory(DateTime startTime, DateTime endTime)
        {
            var result = new List<EnvironmentData>();
            
            foreach (var data in _environmentHistory)
            {
                if (data.Timestamp >= startTime && data.Timestamp <= endTime)
                {
                    result.Add(data);
                }
            }

            return result;
        }

        /// <summary>
        /// 获取实验历史
        /// </summary>
        /// <returns>实验数据列表</returns>
        public List<ExperimentData> GetExperimentHistory()
        {
            return new List<ExperimentData>(_experimentHistory);
        }

        /// <summary>
        /// 清理旧数据
        /// </summary>
        /// <param name="daysToKeep">保留天数</param>
        /// <returns>清理是否成功</returns>
        public async Task<bool> CleanupOldDataAsync(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var files = Directory.GetFiles(_dataDirectory, "*.json");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }

                // 清理内存中的历史数据
                _environmentHistory.RemoveAll(data => data.Timestamp < cutoffDate);
                _experimentHistory.RemoveAll(data => data.StartTime < cutoffDate);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理旧数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导出数据到CSV
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>导出是否成功</returns>
        public async Task<bool> ExportEnvironmentDataToCsvAsync(string filePath, DateTime startTime, DateTime endTime)
        {
            try
            {
                var data = GetEnvironmentHistory(startTime, endTime);
                var csvContent = "Timestamp,Temperature,Humidity,Pressure,Voltage\n";

                foreach (var item in data)
                {
                    csvContent += $"{item.Timestamp:yyyy-MM-dd HH:mm:ss},{item.Temperature},{item.Humidity},{item.Pressure},{item.Voltage}\n";
                }

                await File.WriteAllTextAsync(filePath, csvContent);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出CSV失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public DataStatistics GetDataStatistics()
        {
            return new DataStatistics
            {
                EnvironmentDataCount = _environmentHistory.Count,
                ExperimentDataCount = _experimentHistory.Count,
                DataDirectorySize = GetDirectorySize(_dataDirectory),
                LastUpdateTime = DateTime.Now
            };
        }

        /// <summary>
        /// 获取目录大小
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>目录大小（字节）</returns>
        private long GetDirectorySize(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
                return 0;

            long size = 0;
            var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
            
            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                size += fileInfo.Length;
            }

            return size;
        }
    }

    /// <summary>
    /// 数据统计信息
    /// </summary>
    public class DataStatistics
    {
        public int EnvironmentDataCount { get; set; }
        public int ExperimentDataCount { get; set; }
        public long DataDirectorySize { get; set; }
        public DateTime LastUpdateTime { get; set; }

        public string DataDirectorySizeFormatted
        {
            get
            {
                if (DataDirectorySize < 1024)
                    return $"{DataDirectorySize} B";
                if (DataDirectorySize < 1024 * 1024)
                    return $"{DataDirectorySize / 1024.0:F1} KB";
                if (DataDirectorySize < 1024 * 1024 * 1024)
                    return $"{DataDirectorySize / (1024.0 * 1024.0):F1} MB";
                return $"{DataDirectorySize / (1024.0 * 1024.0 * 1024.0):F1} GB";
            }
        }
    }
}
