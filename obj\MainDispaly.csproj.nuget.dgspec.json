{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\MainDispaly.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\MainDispaly.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\MainDispaly.csproj", "projectName": "MainDispaly", "projectPath": "C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\MainDispaly.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\XH-FILES\\MainDisplay-DEV\\MainDispaly\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["D:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net452"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net452": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net452": {"dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[12.0.3, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}