using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Threading;
using MainDispaly.Models;
using MainDispaly.Services;
using MainDispaly.ViewModels;

namespace MainDispaly.Modules
{
    /// <summary>
    /// 数据采集模块
    /// </summary>
    public class DataCollectionModule : BaseViewModel
    {
        private readonly DataService _dataService;
        private DispatcherTimer _collectionTimer;
        private bool _isCollecting;
        private int _collectionInterval;
        private int _totalDataPoints;
        private int _collectedDataPoints;
        private string _collectionStatus;
        private List<DataPoint> _currentSession;

        /// <summary>
        /// 是否正在采集数据
        /// </summary>
        public bool IsCollecting
        {
            get => _isCollecting;
            set => SetProperty(ref _isCollecting, value);
        }

        /// <summary>
        /// 采集间隔（毫秒）
        /// </summary>
        public int CollectionInterval
        {
            get => _collectionInterval;
            set => SetProperty(ref _collectionInterval, value);
        }

        /// <summary>
        /// 总数据点数
        /// </summary>
        public int TotalDataPoints
        {
            get => _totalDataPoints;
            set => SetProperty(ref _totalDataPoints, value);
        }

        /// <summary>
        /// 已采集数据点数
        /// </summary>
        public int CollectedDataPoints
        {
            get => _collectedDataPoints;
            set => SetProperty(ref _collectedDataPoints, value);
        }

        /// <summary>
        /// 采集状态
        /// </summary>
        public string CollectionStatus
        {
            get => _collectionStatus;
            set => SetProperty(ref _collectionStatus, value);
        }

        /// <summary>
        /// 采集进度百分比
        /// </summary>
        public double CollectionProgress
        {
            get => TotalDataPoints > 0 ? (double)CollectedDataPoints / TotalDataPoints * 100 : 0;
        }

        /// <summary>
        /// 当前会话数据
        /// </summary>
        public List<DataPoint> CurrentSession
        {
            get => _currentSession;
            set => SetProperty(ref _currentSession, value);
        }

        /// <summary>
        /// 数据采集事件
        /// </summary>
        public event EventHandler<DataCollectedEventArgs> DataCollected;

        /// <summary>
        /// 采集完成事件
        /// </summary>
        public event EventHandler<CollectionCompletedEventArgs> CollectionCompleted;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataService">数据服务</param>
        public DataCollectionModule(DataService dataService)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            
            InitializeCollection();
            InitializeTimer();
            
            LogMessage("数据采集模块初始化完成");
        }

        /// <summary>
        /// 初始化采集设置
        /// </summary>
        private void InitializeCollection()
        {
            CollectionInterval = 1000; // 默认1秒采集一次
            TotalDataPoints = 0;
            CollectedDataPoints = 0;
            CollectionStatus = "就绪";
            CurrentSession = new List<DataPoint>();
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _collectionTimer = new DispatcherTimer();
            _collectionTimer.Tick += CollectionTimer_Tick;
        }

        /// <summary>
        /// 开始数据采集
        /// </summary>
        /// <param name="intervalMs">采集间隔（毫秒）</param>
        /// <param name="totalPoints">总采集点数（0表示无限制）</param>
        public async Task<bool> StartCollectionAsync(int intervalMs = 1000, int totalPoints = 0)
        {
            try
            {
                if (IsCollecting)
                {
                    LogMessage("数据采集已在进行中", LogLevel.Warning);
                    return false;
                }

                LogMessage($"开始数据采集，间隔: {intervalMs}ms，总点数: {(totalPoints == 0 ? "无限制" : totalPoints.ToString())}");
                
                CollectionInterval = intervalMs;
                TotalDataPoints = totalPoints;
                CollectedDataPoints = 0;
                CurrentSession.Clear();
                
                _collectionTimer.Interval = TimeSpan.FromMilliseconds(intervalMs);
                _collectionTimer.Start();
                
                IsCollecting = true;
                CollectionStatus = "采集中";
                
                LogMessage("数据采集已启动");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"启动数据采集失败: {ex.Message}", LogLevel.Error);
                CollectionStatus = "启动失败";
                return false;
            }
        }

        /// <summary>
        /// 停止数据采集
        /// </summary>
        public async Task<bool> StopCollectionAsync()
        {
            try
            {
                if (!IsCollecting)
                {
                    LogMessage("数据采集未在进行中", LogLevel.Warning);
                    return false;
                }

                LogMessage("停止数据采集");
                
                _collectionTimer.Stop();
                IsCollecting = false;
                CollectionStatus = "已停止";
                
                // 保存当前会话数据
                await SaveCurrentSessionAsync();
                
                OnCollectionCompleted(new CollectionCompletedEventArgs(CurrentSession.Count, false));
                
                LogMessage($"数据采集已停止，共采集 {CollectedDataPoints} 个数据点");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"停止数据采集失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 暂停数据采集
        /// </summary>
        public void PauseCollection()
        {
            if (IsCollecting)
            {
                _collectionTimer.Stop();
                CollectionStatus = "已暂停";
                LogMessage("数据采集已暂停");
            }
        }

        /// <summary>
        /// 恢复数据采集
        /// </summary>
        public void ResumeCollection()
        {
            if (IsCollecting && CollectionStatus == "已暂停")
            {
                _collectionTimer.Start();
                CollectionStatus = "采集中";
                LogMessage("数据采集已恢复");
            }
        }

        /// <summary>
        /// 手动采集单个数据点
        /// </summary>
        public async Task<DataPoint> CollectSingleDataPointAsync()
        {
            try
            {
                var dataPoint = GenerateDataPoint();
                CurrentSession.Add(dataPoint);
                
                if (IsCollecting)
                {
                    CollectedDataPoints++;
                    OnPropertyChanged(nameof(CollectionProgress));
                }
                
                OnDataCollected(new DataCollectedEventArgs(dataPoint));
                
                return dataPoint;
            }
            catch (Exception ex)
            {
                LogMessage($"采集单个数据点失败: {ex.Message}", LogLevel.Error);
                return null;
            }
        }

        /// <summary>
        /// 生成数据点
        /// </summary>
        private DataPoint GenerateDataPoint()
        {
            var random = new Random();
            
            return new DataPoint
            {
                Timestamp = DateTime.Now,
                Temperature = 20 + random.NextDouble() * 10,
                Humidity = 40 + random.NextDouble() * 20,
                Pressure = 100000 + random.NextDouble() * 2000,
                Voltage = 215 + random.NextDouble() * 10,
                ElectromagneticField = random.NextDouble() * 100,
                RadiationLevel = random.NextDouble() * 50
            };
        }

        /// <summary>
        /// 保存当前会话数据
        /// </summary>
        private async Task SaveCurrentSessionAsync()
        {
            try
            {
                if (CurrentSession.Count == 0)
                    return;

                LogMessage($"保存会话数据，共 {CurrentSession.Count} 个数据点");
                
                // 这里可以实现具体的数据保存逻辑
                // 例如保存到数据库或文件
                
                LogMessage("会话数据保存完成");
            }
            catch (Exception ex)
            {
                LogMessage($"保存会话数据失败: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导出格式</param>
        public async Task<bool> ExportDataAsync(string filePath, ExportFormat format = ExportFormat.CSV)
        {
            try
            {
                LogMessage($"导出数据到: {filePath}，格式: {format}");
                
                switch (format)
                {
                    case ExportFormat.CSV:
                        await ExportToCsvAsync(filePath);
                        break;
                    case ExportFormat.JSON:
                        await ExportToJsonAsync(filePath);
                        break;
                    case ExportFormat.XML:
                        await ExportToXmlAsync(filePath);
                        break;
                    default:
                        throw new ArgumentException("不支持的导出格式");
                }
                
                LogMessage("数据导出完成");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"导出数据失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 导出为CSV格式
        /// </summary>
        private async Task ExportToCsvAsync(string filePath)
        {
            var csvContent = "Timestamp,Temperature,Humidity,Pressure,Voltage,ElectromagneticField,RadiationLevel\n";
            
            foreach (var dataPoint in CurrentSession)
            {
                csvContent += $"{dataPoint.Timestamp:yyyy-MM-dd HH:mm:ss.fff}," +
                             $"{dataPoint.Temperature:F2}," +
                             $"{dataPoint.Humidity:F2}," +
                             $"{dataPoint.Pressure:F2}," +
                             $"{dataPoint.Voltage:F2}," +
                             $"{dataPoint.ElectromagneticField:F2}," +
                             $"{dataPoint.RadiationLevel:F2}\n";
            }
            
            await System.IO.File.WriteAllTextAsync(filePath, csvContent);
        }

        /// <summary>
        /// 导出为JSON格式
        /// </summary>
        private async Task ExportToJsonAsync(string filePath)
        {
            var jsonContent = System.Text.Json.JsonSerializer.Serialize(CurrentSession, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });
            
            await System.IO.File.WriteAllTextAsync(filePath, jsonContent);
        }

        /// <summary>
        /// 导出为XML格式
        /// </summary>
        private async Task ExportToXmlAsync(string filePath)
        {
            // 这里可以实现XML导出逻辑
            throw new NotImplementedException("XML导出功能尚未实现");
        }

        /// <summary>
        /// 采集定时器事件
        /// </summary>
        private async void CollectionTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                await CollectSingleDataPointAsync();
                
                // 检查是否达到总数据点数
                if (TotalDataPoints > 0 && CollectedDataPoints >= TotalDataPoints)
                {
                    await StopCollectionAsync();
                    OnCollectionCompleted(new CollectionCompletedEventArgs(CollectedDataPoints, true));
                }
            }
            catch (Exception ex)
            {
                LogMessage($"定时采集数据时发生错误: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 触发数据采集事件
        /// </summary>
        private void OnDataCollected(DataCollectedEventArgs e)
        {
            DataCollected?.Invoke(this, e);
        }

        /// <summary>
        /// 触发采集完成事件
        /// </summary>
        private void OnCollectionCompleted(CollectionCompletedEventArgs e)
        {
            CollectionCompleted?.Invoke(this, e);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _collectionTimer?.Stop();
            LogMessage("数据采集模块资源已释放");
        }
    }

    /// <summary>
    /// 数据点模型
    /// </summary>
    public class DataPoint
    {
        public DateTime Timestamp { get; set; }
        public double Temperature { get; set; }
        public double Humidity { get; set; }
        public double Pressure { get; set; }
        public double Voltage { get; set; }
        public double ElectromagneticField { get; set; }
        public double RadiationLevel { get; set; }
    }

    /// <summary>
    /// 导出格式枚举
    /// </summary>
    public enum ExportFormat
    {
        CSV,
        JSON,
        XML
    }

    /// <summary>
    /// 数据采集事件参数
    /// </summary>
    public class DataCollectedEventArgs : EventArgs
    {
        public DataPoint DataPoint { get; }

        public DataCollectedEventArgs(DataPoint dataPoint)
        {
            DataPoint = dataPoint;
        }
    }

    /// <summary>
    /// 采集完成事件参数
    /// </summary>
    public class CollectionCompletedEventArgs : EventArgs
    {
        public int TotalDataPoints { get; }
        public bool CompletedNormally { get; }

        public CollectionCompletedEventArgs(int totalDataPoints, bool completedNormally)
        {
            TotalDataPoints = totalDataPoints;
            CompletedNormally = completedNormally;
        }
    }
}
