<Window x:Class="MainDispaly.TestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="数据大屏样式测试"
        Height="800"
        Width="1200"
        WindowState="Normal">

    <!-- 引用数据大屏样式资源 -->
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/DataScreenStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 专业数据大屏背景 -->
    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0A1426" Offset="0"/>
            <GradientStop Color="#1A2B42" Offset="0.3"/>
            <GradientStop Color="#0F1E33" Offset="0.7"/>
            <GradientStop Color="#0A1426" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>

    <!-- 测试布局 -->
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="专业数据大屏样式测试"
                   Style="{StaticResource MainTitleStyle}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- 测试面板 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧面板 -->
            <Border Grid.Column="0" Style="{StaticResource ModernCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题栏 -->
                    <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                        <TextBlock Text="系统监控" Style="{StaticResource HeaderTextStyle}"/>
                    </Border>

                    <!-- 内容区域 -->
                    <StackPanel Grid.Row="1" Margin="15">
                        <TextBlock Text="CPU使用率" 
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="45%" 
                                   Style="{StaticResource ImportantDataStyle}"
                                   Margin="0,0,0,15"/>
                        
                        <TextBlock Text="内存使用率" 
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="67%" 
                                   Style="{StaticResource ImportantDataStyle}"
                                   Margin="0,0,0,15"/>

                        <Button Content="刷新数据" 
                                Style="{StaticResource ModernButtonStyle}"
                                HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 中间面板 -->
            <Border Grid.Column="1" Style="{StaticResource ModernCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题栏 -->
                    <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                        <TextBlock Text="实验进度" Style="{StaticResource HeaderTextStyle}"/>
                    </Border>

                    <!-- 内容区域 -->
                    <StackPanel Grid.Row="1" Margin="15">
                        <TextBlock Text="当前阶段" 
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="数据采集" 
                                   Foreground="{StaticResource AccentCyanBrush}"
                                   FontSize="16" FontWeight="Bold"
                                   Margin="0,0,0,15"/>
                        
                        <TextBlock Text="完成度" 
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   FontSize="12" Margin="0,0,0,5"/>
                        <ProgressBar Style="{StaticResource ModernProgressBarStyle}"
                                     Value="75" Maximum="100"
                                     Margin="0,0,0,10"/>
                        <TextBlock Text="75%" 
                                   Foreground="{StaticResource AccentCyanBrush}"
                                   FontSize="14" FontWeight="Bold"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 右侧面板 -->
            <Border Grid.Column="2" Style="{StaticResource ModernCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题栏 -->
                    <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                        <TextBlock Text="设备状态" Style="{StaticResource HeaderTextStyle}"/>
                    </Border>

                    <!-- 内容区域 -->
                    <StackPanel Grid.Row="1" Margin="15">
                        <Border Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="4" Padding="10,8" Margin="0,0,0,8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="传感器1"
                                          Foreground="{StaticResource TextPrimaryBrush}"
                                          VerticalAlignment="Center"/>
                                <Ellipse Grid.Column="1" Style="{StaticResource StatusDotStyle}"/>
                            </Grid>
                        </Border>

                        <Border Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="4" Padding="10,8" Margin="0,0,0,8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="传感器2"
                                          Foreground="{StaticResource TextPrimaryBrush}"
                                          VerticalAlignment="Center"/>
                                <Ellipse Grid.Column="1" Style="{StaticResource WarningDotStyle}"/>
                            </Grid>
                        </Border>

                        <Border Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="4" Padding="10,8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="传感器3"
                                          Foreground="{StaticResource TextPrimaryBrush}"
                                          VerticalAlignment="Center"/>
                                <Ellipse Grid.Column="1" Style="{StaticResource ErrorDotStyle}"/>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
