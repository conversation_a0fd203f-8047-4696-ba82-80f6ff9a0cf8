using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using MainDispaly.Models;
using MainDispaly.Services;
using MainDispaly.ViewModels;

namespace MainDispaly.Modules
{
    /// <summary>
    /// 3D视频展示模块
    /// </summary>
    public class Video3DModule : BaseViewModel
    {
        private readonly VideoService _videoService;
        private VideoSource _mainVideoSource;
        private bool _isPlaying;
        private bool _isPaused;
        private double _playbackPosition;
        private double _duration;
        private string _playbackStatus;
        private DispatcherTimer _playbackTimer;

        /// <summary>
        /// 主视频源
        /// </summary>
        public VideoSource MainVideoSource
        {
            get => _mainVideoSource;
            set => SetProperty(ref _mainVideoSource, value);
        }

        /// <summary>
        /// 是否正在播放
        /// </summary>
        public bool IsPlaying
        {
            get => _isPlaying;
            set => SetProperty(ref _isPlaying, value);
        }

        /// <summary>
        /// 是否已暂停
        /// </summary>
        public bool IsPaused
        {
            get => _isPaused;
            set => SetProperty(ref _isPaused, value);
        }

        /// <summary>
        /// 播放位置（秒）
        /// </summary>
        public double PlaybackPosition
        {
            get => _playbackPosition;
            set => SetProperty(ref _playbackPosition, value);
        }

        /// <summary>
        /// 视频总时长（秒）
        /// </summary>
        public double Duration
        {
            get => _duration;
            set => SetProperty(ref _duration, value);
        }

        /// <summary>
        /// 播放状态文本
        /// </summary>
        public string PlaybackStatus
        {
            get => _playbackStatus;
            set => SetProperty(ref _playbackStatus, value);
        }

        /// <summary>
        /// 播放进度百分比
        /// </summary>
        public double PlaybackProgress
        {
            get => Duration > 0 ? (PlaybackPosition / Duration) * 100 : 0;
        }

        /// <summary>
        /// 剩余时间
        /// </summary>
        public TimeSpan RemainingTime
        {
            get => TimeSpan.FromSeconds(Math.Max(0, Duration - PlaybackPosition));
        }

        /// <summary>
        /// 播放状态变化事件
        /// </summary>
        public event EventHandler<PlaybackStatusChangedEventArgs> PlaybackStatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="videoService">视频服务</param>
        public Video3DModule(VideoService videoService)
        {
            _videoService = videoService ?? throw new ArgumentNullException(nameof(videoService));
            
            InitializeVideoSource();
            InitializeTimer();
            
            LogMessage("3D视频模块初始化完成");
        }

        /// <summary>
        /// 初始化视频源
        /// </summary>
        private void InitializeVideoSource()
        {
            MainVideoSource = new VideoSource("3D展示视频", "file://demo_3d.mp4", VideoSourceType.File);
            MainVideoSource.PropertyChanged += MainVideoSource_PropertyChanged;
            
            _videoService.RegisterVideoSource(MainVideoSource);
            
            // 设置默认视频信息
            Duration = 300; // 5分钟示例视频
            PlaybackPosition = 0;
            PlaybackStatus = "就绪";
        }

        /// <summary>
        /// 初始化播放定时器
        /// </summary>
        private void InitializeTimer()
        {
            _playbackTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 每100ms更新一次
            };
            _playbackTimer.Tick += PlaybackTimer_Tick;
        }

        /// <summary>
        /// 加载视频文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public async Task<bool> LoadVideoAsync(string filePath)
        {
            try
            {
                LogMessage($"正在加载视频文件: {filePath}");
                
                MainVideoSource.SourceUrl = filePath;
                MainVideoSource.UpdateStatus(VideoStatus.Connecting, "正在加载视频...");
                
                // 模拟视频加载过程
                await Task.Delay(1000);
                
                var success = await _videoService.ConnectVideoSourceAsync(MainVideoSource.Name);
                
                if (success)
                {
                    MainVideoSource.UpdateStatus(VideoStatus.Connected, "视频加载成功");
                    PlaybackStatus = "已加载";
                    LogMessage("视频加载成功");
                }
                else
                {
                    MainVideoSource.UpdateStatus(VideoStatus.Error, "视频加载失败");
                    PlaybackStatus = "加载失败";
                    LogMessage("视频加载失败", LogLevel.Error);
                }
                
                return success;
            }
            catch (Exception ex)
            {
                LogMessage($"加载视频时发生错误: {ex.Message}", LogLevel.Error);
                MainVideoSource.UpdateStatus(VideoStatus.Error, "加载错误");
                PlaybackStatus = "加载错误";
                return false;
            }
        }

        /// <summary>
        /// 播放视频
        /// </summary>
        public async Task<bool> PlayAsync()
        {
            try
            {
                if (MainVideoSource.Status != VideoStatus.Connected && MainVideoSource.Status != VideoStatus.Paused)
                {
                    LogMessage("视频未就绪，无法播放", LogLevel.Warning);
                    return false;
                }

                LogMessage("开始播放3D视频");
                
                var success = await _videoService.StartPlaybackAsync(MainVideoSource.Name);
                
                if (success)
                {
                    IsPlaying = true;
                    IsPaused = false;
                    PlaybackStatus = "播放中";
                    _playbackTimer.Start();
                    
                    OnPlaybackStatusChanged(new PlaybackStatusChangedEventArgs(PlaybackState.Playing));
                }
                
                return success;
            }
            catch (Exception ex)
            {
                LogMessage($"播放视频时发生错误: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 暂停播放
        /// </summary>
        public async Task<bool> PauseAsync()
        {
            try
            {
                if (!IsPlaying)
                    return false;

                LogMessage("暂停3D视频播放");
                
                var success = await _videoService.PausePlaybackAsync(MainVideoSource.Name);
                
                if (success)
                {
                    IsPlaying = false;
                    IsPaused = true;
                    PlaybackStatus = "已暂停";
                    _playbackTimer.Stop();
                    
                    OnPlaybackStatusChanged(new PlaybackStatusChangedEventArgs(PlaybackState.Paused));
                }
                
                return success;
            }
            catch (Exception ex)
            {
                LogMessage($"暂停视频时发生错误: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 停止播放
        /// </summary>
        public async Task<bool> StopAsync()
        {
            try
            {
                LogMessage("停止3D视频播放");
                
                var success = await _videoService.StopPlaybackAsync(MainVideoSource.Name);
                
                if (success)
                {
                    IsPlaying = false;
                    IsPaused = false;
                    PlaybackPosition = 0;
                    PlaybackStatus = "已停止";
                    _playbackTimer.Stop();
                    
                    OnPlaybackStatusChanged(new PlaybackStatusChangedEventArgs(PlaybackState.Stopped));
                }
                
                return success;
            }
            catch (Exception ex)
            {
                LogMessage($"停止视频时发生错误: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 跳转到指定位置
        /// </summary>
        /// <param name="position">位置（秒）</param>
        public async Task<bool> SeekToAsync(double position)
        {
            try
            {
                if (position < 0 || position > Duration)
                    return false;

                LogMessage($"跳转到位置: {position:F1}秒");
                
                PlaybackPosition = position;
                OnPropertyChanged(nameof(PlaybackProgress));
                OnPropertyChanged(nameof(RemainingTime));
                
                // 这里应该实现实际的跳转逻辑
                await Task.Delay(100);
                
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"跳转视频位置时发生错误: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 播放定时器事件
        /// </summary>
        private void PlaybackTimer_Tick(object sender, EventArgs e)
        {
            if (IsPlaying && !IsPaused)
            {
                PlaybackPosition += 0.1; // 每100ms增加0.1秒
                
                if (PlaybackPosition >= Duration)
                {
                    // 播放完成
                    _ = StopAsync();
                    OnPlaybackStatusChanged(new PlaybackStatusChangedEventArgs(PlaybackState.Completed));
                }
                
                OnPropertyChanged(nameof(PlaybackProgress));
                OnPropertyChanged(nameof(RemainingTime));
            }
        }

        /// <summary>
        /// 视频源属性变化事件
        /// </summary>
        private void MainVideoSource_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(VideoSource.Status))
            {
                LogMessage($"3D视频状态变化: {MainVideoSource.StatusText}");
            }
        }

        /// <summary>
        /// 触发播放状态变化事件
        /// </summary>
        private void OnPlaybackStatusChanged(PlaybackStatusChangedEventArgs e)
        {
            PlaybackStatusChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _playbackTimer?.Stop();
            
            if (MainVideoSource != null)
            {
                MainVideoSource.PropertyChanged -= MainVideoSource_PropertyChanged;
                _videoService.UnregisterVideoSource(MainVideoSource.Name);
            }
            
            LogMessage("3D视频模块资源已释放");
        }
    }

    /// <summary>
    /// 播放状态枚举
    /// </summary>
    public enum PlaybackState
    {
        Stopped,
        Playing,
        Paused,
        Completed
    }

    /// <summary>
    /// 播放状态变化事件参数
    /// </summary>
    public class PlaybackStatusChangedEventArgs : EventArgs
    {
        public PlaybackState State { get; private set; }

        public PlaybackStatusChangedEventArgs(PlaybackState state)
        {
            State = state;
        }
    }
}
