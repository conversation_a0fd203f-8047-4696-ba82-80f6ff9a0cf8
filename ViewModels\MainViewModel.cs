using System;
using System.Collections.ObjectModel;
using System.Windows.Threading;
using MainDispaly.Models;
using MainDispaly.Services;

namespace MainDispaly.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public class MainViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private readonly VideoService _videoService;
        private readonly DispatcherTimer _updateTimer;

        private ExperimentData _experimentData;
        private DeviceStatus _deviceStatus;
        private ObservableCollection<VideoSource> _videoSources;
        private VideoSource _mainVideoSource;
        private string _systemTime;
        private string _systemStatus;

        /// <summary>
        /// 实验数据
        /// </summary>
        public ExperimentData ExperimentData
        {
            get => _experimentData;
            set => SetProperty(ref _experimentData, value);
        }

        /// <summary>
        /// 设备状态
        /// </summary>
        public DeviceStatus DeviceStatus
        {
            get => _deviceStatus;
            set => SetProperty(ref _deviceStatus, value);
        }

        /// <summary>
        /// 视频源列表
        /// </summary>
        public ObservableCollection<VideoSource> VideoSources
        {
            get => _videoSources;
            set => SetProperty(ref _videoSources, value);
        }

        /// <summary>
        /// 主视频源（3D展示区域）
        /// </summary>
        public VideoSource MainVideoSource
        {
            get => _mainVideoSource;
            set => SetProperty(ref _mainVideoSource, value);
        }

        /// <summary>
        /// 系统时间
        /// </summary>
        public string SystemTime
        {
            get => _systemTime;
            set => SetProperty(ref _systemTime, value);
        }

        /// <summary>
        /// 系统状态
        /// </summary>
        public string SystemStatus
        {
            get => _systemStatus;
            set => SetProperty(ref _systemStatus, value);
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainViewModel()
        {
            _dataService = new DataService();
            _videoService = new VideoService();

            InitializeData();
            InitializeTimer();
            
            LogMessage("主ViewModel初始化完成");
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化实验数据
            ExperimentData = new ExperimentData();

            // 初始化设备状态
            DeviceStatus = new DeviceStatus();

            // 初始化视频源
            VideoSources = new ObservableCollection<VideoSource>
            {
                new VideoSource("摄像头 #1", "camera://1", VideoSourceType.Camera),
                new VideoSource("摄像头 #2", "camera://2", VideoSourceType.Camera),
                new VideoSource("摄像头 #3", "camera://3", VideoSourceType.Camera),
                new VideoSource("摄像头 #4", "camera://4", VideoSourceType.Camera)
            };

            // 初始化主视频源
            MainVideoSource = new VideoSource("3D展示视频", "file://3d_demo.mp4", VideoSourceType.File);

            // 设置初始状态
            SystemStatus = "系统正常";
            UpdateSystemTime();

            // 模拟视频源连接
            foreach (var videoSource in VideoSources)
            {
                videoSource.UpdateStatus(VideoStatus.Connected, "摄像头连接正常");
            }
            MainVideoSource.UpdateStatus(VideoStatus.Connected, "3D视频就绪");
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _updateTimer.Tick += UpdateTimer_Tick;
            _updateTimer.Start();
        }

        /// <summary>
        /// 定时器更新事件
        /// </summary>
        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            UpdateSystemTime();
            UpdateEnvironmentData();
            UpdateSystemStatus();
        }

        /// <summary>
        /// 更新系统时间
        /// </summary>
        private void UpdateSystemTime()
        {
            SystemTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 更新环境数据
        /// </summary>
        private void UpdateEnvironmentData()
        {
            // 模拟环境数据更新
            DeviceStatus.EnvironmentData.GenerateRandomData();
        }

        /// <summary>
        /// 更新系统状态
        /// </summary>
        private void UpdateSystemStatus()
        {
            var overallStatus = DeviceStatus.OverallStatus;
            switch (overallStatus)
            {
                case ComponentStatus.Normal:
                    SystemStatus = "系统正常";
                    break;
                case ComponentStatus.Warning:
                    SystemStatus = "系统警告";
                    break;
                case ComponentStatus.Error:
                    SystemStatus = "系统错误";
                    break;
                default:
                    SystemStatus = "系统未知";
                    break;
            }
        }

        /// <summary>
        /// 开始实验
        /// </summary>
        public void StartExperiment()
        {
            if (ExperimentData.Status == ExperimentStatus.Ready)
            {
                ExperimentData.Status = ExperimentStatus.Running;
                ExperimentData.StartTime = DateTime.Now;
                LogMessage("实验开始");
            }
        }

        /// <summary>
        /// 暂停实验
        /// </summary>
        public void PauseExperiment()
        {
            if (ExperimentData.Status == ExperimentStatus.Running)
            {
                ExperimentData.Status = ExperimentStatus.Paused;
                LogMessage("实验暂停");
            }
        }

        /// <summary>
        /// 停止实验
        /// </summary>
        public void StopExperiment()
        {
            if (ExperimentData.Status == ExperimentStatus.Running || 
                ExperimentData.Status == ExperimentStatus.Paused)
            {
                ExperimentData.Status = ExperimentStatus.Completed;
                ExperimentData.EndTime = DateTime.Now;
                LogMessage("实验结束");
            }
        }

        /// <summary>
        /// 播放主视频
        /// </summary>
        public void PlayMainVideo()
        {
            if (MainVideoSource.Status == VideoStatus.Connected || 
                MainVideoSource.Status == VideoStatus.Paused)
            {
                MainVideoSource.UpdateStatus(VideoStatus.Playing, "视频播放中");
                LogMessage("开始播放3D视频");
            }
        }

        /// <summary>
        /// 暂停主视频
        /// </summary>
        public void PauseMainVideo()
        {
            if (MainVideoSource.Status == VideoStatus.Playing)
            {
                MainVideoSource.UpdateStatus(VideoStatus.Paused, "视频已暂停");
                LogMessage("暂停3D视频");
            }
        }

        /// <summary>
        /// 停止主视频
        /// </summary>
        public void StopMainVideo()
        {
            if (MainVideoSource.Status == VideoStatus.Playing || 
                MainVideoSource.Status == VideoStatus.Paused)
            {
                MainVideoSource.UpdateStatus(VideoStatus.Connected, "视频已停止");
                LogMessage("停止3D视频");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _updateTimer?.Stop();
            _videoService?.Dispose();
            LogMessage("主ViewModel资源已释放");
        }
    }
}
