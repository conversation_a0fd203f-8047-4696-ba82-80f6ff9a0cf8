using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;

namespace MainDispaly.Models
{
    /// <summary>
    /// 设备状态模型
    /// </summary>
    public class DeviceStatus : INotifyPropertyChanged
    {
        private ObservableCollection<SystemComponent> _systemComponents;
        private EnvironmentData _environmentData;
        private DateTime _lastUpdateTime;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 系统组件列表
        /// </summary>
        public ObservableCollection<SystemComponent> SystemComponents
        {
            get => _systemComponents;
            set
            {
                _systemComponents = value;
                OnPropertyChanged(nameof(SystemComponents));
            }
        }

        /// <summary>
        /// 环境数据
        /// </summary>
        public EnvironmentData EnvironmentData
        {
            get => _environmentData;
            set
            {
                _environmentData = value;
                OnPropertyChanged(nameof(EnvironmentData));
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                _lastUpdateTime = value;
                OnPropertyChanged(nameof(LastUpdateTime));
            }
        }

        /// <summary>
        /// 整体系统状态
        /// </summary>
        public ComponentStatus OverallStatus
        {
            get
            {
                if (SystemComponents == null || SystemComponents.Count == 0)
                    return ComponentStatus.Unknown;

                bool hasError = false;
                bool hasWarning = false;

                foreach (var component in SystemComponents)
                {
                    switch (component.Status)
                    {
                        case ComponentStatus.Error:
                            hasError = true;
                            break;
                        case ComponentStatus.Warning:
                            hasWarning = true;
                            break;
                    }
                }

                if (hasError) return ComponentStatus.Error;
                if (hasWarning) return ComponentStatus.Warning;
                return ComponentStatus.Normal;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceStatus()
        {
            SystemComponents = new ObservableCollection<SystemComponent>();
            EnvironmentData = new EnvironmentData();
            InitializeDefaultComponents();
        }

        /// <summary>
        /// 初始化默认组件
        /// </summary>
        private void InitializeDefaultComponents()
        {
            SystemComponents.Add(new SystemComponent
            {
                Name = "数采系统",
                Status = ComponentStatus.Normal,
                Description = "数据采集系统运行正常"
            });

            SystemComponents.Add(new SystemComponent
            {
                Name = "真空系统",
                Status = ComponentStatus.Normal,
                Description = "真空系统运行正常"
            });

            SystemComponents.Add(new SystemComponent
            {
                Name = "充放电系统",
                Status = ComponentStatus.Warning,
                Description = "充放电系统电压偏低"
            });

            SystemComponents.Add(new SystemComponent
            {
                Name = "触发系统",
                Status = ComponentStatus.Normal,
                Description = "触发系统就绪"
            });

            SystemComponents.Add(new SystemComponent
            {
                Name = "远程加电系统",
                Status = ComponentStatus.Normal,
                Description = "远程加电系统正常"
            });

            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新组件状态
        /// </summary>
        /// <param name="componentName">组件名称</param>
        /// <param name="status">新状态</param>
        /// <param name="description">状态描述</param>
        public void UpdateComponentStatus(string componentName, ComponentStatus status, string description = null)
        {
            var component = SystemComponents.FirstOrDefault(c => c.Name == componentName);
            if (component != null)
            {
                component.Status = status;
                if (!string.IsNullOrEmpty(description))
                    component.Description = description;
                
                LastUpdateTime = DateTime.Now;
                OnPropertyChanged(nameof(OverallStatus));
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 系统组件模型
    /// </summary>
    public class SystemComponent : INotifyPropertyChanged
    {
        private string _name;
        private ComponentStatus _status;
        private string _description;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 组件名称
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        /// <summary>
        /// 组件状态
        /// </summary>
        public ComponentStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged(nameof(Description));
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 组件状态枚举
    /// </summary>
    public enum ComponentStatus
    {
        Unknown,    // 未知
        Normal,     // 正常
        Warning,    // 警告
        Error       // 错误
    }
}
