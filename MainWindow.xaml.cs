﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using MainDispaly.ViewModels;
using MainDispaly.Modules;
using MainDispaly.Services;

namespace MainDispaly
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private DispatcherTimer timer;
        private MainViewModel _viewModel;
        private VideoMonitoringModule _videoMonitoringModule;
        private VideoService _videoService;
        private DataService _dataService;

        public MainWindow()
        {
            InitializeComponent();
            InitializeServices();
            InitializeModules();
            InitializeViewModel();
            InitializeTimer();
            InitializeWindow();

            this.Loaded += MainWindow_Loaded;
            this.Closing += MainWindow_Closing;
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        private void InitializeServices()
        {
            _videoService = new VideoService();
            _dataService = new DataService();
        }

        /// <summary>
        /// 初始化模块
        /// </summary>
        private void InitializeModules()
        {
            _videoMonitoringModule = new VideoMonitoringModule(_videoService);
            _videoMonitoringModule.CameraStatusChanged += VideoMonitoringModule_CameraStatusChanged;
        }

        /// <summary>
        /// 初始化ViewModel
        /// </summary>
        private void InitializeViewModel()
        {
            _viewModel = new MainViewModel();
            this.DataContext = _viewModel;
        }

        /// <summary>
        /// 初始化时间显示定时器
        /// </summary>
        private void InitializeTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
            timer.Start();
        }

        /// <summary>
        /// 定时器事件，更新系统时间显示
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            if (SystemTimeDisplay != null)
            {
                SystemTimeDisplay.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        /// <summary>
        /// 初始化窗口设置
        /// </summary>
        private void InitializeWindow()
        {
            // 设置窗口全屏
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // 添加键盘事件处理
            this.KeyDown += MainWindow_KeyDown;
        }

        /// <summary>
        /// 键盘事件处理，ESC键退出全屏
        /// </summary>
        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                // ESC键切换窗口模式
                if (this.WindowState == WindowState.Maximized && this.WindowStyle == WindowStyle.None)
                {
                    this.WindowStyle = WindowStyle.SingleBorderWindow;
                    this.WindowState = WindowState.Normal;
                }
                else
                {
                    this.WindowStyle = WindowStyle.None;
                    this.WindowState = WindowState.Maximized;
                }
            }
            else if (e.Key == Key.F11)
            {
                // F11键全屏切换
                if (this.WindowState == WindowState.Maximized)
                {
                    this.WindowState = WindowState.Normal;
                }
                else
                {
                    this.WindowState = WindowState.Maximized;
                }
            }
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 启动视频监控
                await _videoMonitoringModule.StartMonitoringAsync();

                // 更新系统状态
                UpdateSystemStatus("系统初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"系统初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        private async void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 停止视频监控
                if (_videoMonitoringModule != null)
                {
                    await _videoMonitoringModule.StopMonitoringAsync();
                    _videoMonitoringModule.Dispose();
                }

                // 释放服务资源
                _videoService?.Dispose();
                _viewModel?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 摄像头状态变化事件
        /// </summary>
        private void VideoMonitoringModule_CameraStatusChanged(object sender, CameraStatusChangedEventArgs e)
        {
            // 在UI线程中更新状态
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateCameraStatus(e.Camera);
            }));
        }

        /// <summary>
        /// 更新摄像头状态
        /// </summary>
        private void UpdateCameraStatus(Models.VideoSource camera)
        {
            // 这里可以更新UI中对应摄像头的状态显示
            System.Diagnostics.Debug.WriteLine($"摄像头 {camera.Name} 状态更新: {camera.StatusText}");
        }

        /// <summary>
        /// 更新系统状态
        /// </summary>
        private void UpdateSystemStatus(string status)
        {
            System.Diagnostics.Debug.WriteLine($"系统状态: {status}");
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            base.OnClosed(e);
        }
    }
}
