<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 专业数据大屏配色方案 -->
    <!-- 主色调：深蓝科技感 -->
    <Color x:Key="PrimaryDarkColor">#0B1426</Color>
    <Color x:Key="PrimaryMediumColor">#1A2B42</Color>
    <Color x:Key="PrimaryLightColor">#2A4A73</Color>
    
    <!-- 强调色：青蓝色系 -->
    <Color x:Key="AccentCyanColor">#4FC3F7</Color>
    <Color x:Key="AccentBlueColor">#29B6F6</Color>
    <Color x:Key="AccentTealColor">#26C6DA</Color>
    
    <!-- 功能色 -->
    <Color x:Key="SuccessColor">#4CAF50</Color>
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="ErrorColor">#F44336</Color>
    <Color x:Key="InfoColor">#2196F3</Color>
    
    <!-- 文本色 -->
    <Color x:Key="TextPrimaryColor">#ECEFF1</Color>
    <Color x:Key="TextSecondaryColor">#B0BEC5</Color>
    <Color x:Key="TextAccentColor">#4FC3F7</Color>
    
    <!-- 发光效果色 -->
    <Color x:Key="GlowColor">#4FC3F7</Color>
    
    <!-- 画刷资源 -->
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryMediumBrush" Color="{StaticResource PrimaryMediumColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    
    <SolidColorBrush x:Key="AccentCyanBrush" Color="{StaticResource AccentCyanColor}"/>
    <SolidColorBrush x:Key="AccentBlueBrush" Color="{StaticResource AccentBlueColor}"/>
    <SolidColorBrush x:Key="AccentTealBrush" Color="{StaticResource AccentTealColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextAccentBrush" Color="{StaticResource TextAccentColor}"/>
    
    <SolidColorBrush x:Key="GlowBrush" Color="{StaticResource GlowColor}"/>
    
    <!-- 渐变画刷 -->
    <LinearGradientBrush x:Key="PanelBackgroundBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#0B1426" Offset="0"/>
        <GradientStop Color="#1A2B42" Offset="0.3"/>
        <GradientStop Color="#0F1E33" Offset="0.7"/>
        <GradientStop Color="#0B1426" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="HeaderBackgroundBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="#1A4B73" Offset="0"/>
        <GradientStop Color="#2A5F8A" Offset="0.5"/>
        <GradientStop Color="#1A4B73" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="BorderGradientBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="#29B6F6" Offset="0"/>
        <GradientStop Color="#4FC3F7" Offset="0.5"/>
        <GradientStop Color="#29B6F6" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- 内容区域背景 -->
    <SolidColorBrush x:Key="ContentBackgroundBrush" Color="#1A2B42"/>
    <SolidColorBrush x:Key="TechBackgroundBrush" Color="#0F1E33"/>
    
    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="GlowBorderBrush" Color="#4FC3F7"/>
    
    <!-- 橙色强调色（用于特殊模块） -->
    <Color x:Key="AccentOrangeColor">#FF9800</Color>
    <SolidColorBrush x:Key="AccentOrangeBrush" Color="{StaticResource AccentOrangeColor}"/>

    <!-- 增强科技感的数据大屏卡片样式 -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <!-- 更深的背景色，与主背景形成层次 -->
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#0D1B2F" Offset="0"/>
                    <GradientStop Color="#1A2F4A" Offset="0.3"/>
                    <GradientStop Color="#122238" Offset="0.7"/>
                    <GradientStop Color="#0D1B2F" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <!-- 科技感边框渐变 -->
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#1976D2" Offset="0"/>
                    <GradientStop Color="#42A5F5" Offset="0.3"/>
                    <GradientStop Color="#29B6F6" Offset="0.5"/>
                    <GradientStop Color="#42A5F5" Offset="0.7"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <!-- 增强发光效果 -->
                <DropShadowEffect Color="#42A5F5" BlurRadius="12" ShadowDepth="0" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 科技感增强的标题栏样式 -->
    <Style x:Key="ModernHeaderStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <!-- 更具科技感的标题栏背景 -->
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#1565C0" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="0.2"/>
                    <GradientStop Color="#1E88E5" Offset="0.5"/>
                    <GradientStop Color="#1976D2" Offset="0.8"/>
                    <GradientStop Color="#1565C0" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Height" Value="45"/>
        <Setter Property="CornerRadius" Value="4,4,0,0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="20,0"/>
        <Setter Property="BorderBrush" Value="#42A5F5"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#1976D2" BlurRadius="6" ShadowDepth="0" Opacity="0.6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 科技感大标题文字样式 -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Effect">
            <Setter.Value>
                <!-- 增强文字发光效果 -->
                <DropShadowEffect Color="#E3F2FD" BlurRadius="4" ShadowDepth="0" Opacity="0.9"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="10"/>
        <Setter Property="Height" Value="10"/>
        <Setter Property="Fill" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource SuccessColor}" BlurRadius="4" ShadowDepth="0" Opacity="0.7"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 内容区域样式 -->
    <Style x:Key="ContentAreaStyle" TargetType="Grid">
        <Setter Property="Margin" Value="8"/>
    </Style>

    <!-- 科技感内容区域样式 -->
    <Style x:Key="TechContentAreaStyle" TargetType="Grid">
        <Setter Property="Margin" Value="0"/>
    </Style>

    <!-- 科技感网格背景样式 -->
    <Style x:Key="TechGridBackgroundStyle" TargetType="Border">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Opacity" Value="0.3"/>
    </Style>

    <!-- 科技感装饰线条样式 -->
    <Style x:Key="TechDecorativeLineStyle" TargetType="Border">
        <Setter Property="Height" Value="1"/>
        <Setter Property="Background" Value="{StaticResource GlowBorderBrush}"/>
        <Setter Property="Opacity" Value="0.4"/>
    </Style>

    <!-- 科技感角落装饰样式 -->
    <Style x:Key="TechCornerDecorStyle" TargetType="Border">
        <Setter Property="Width" Value="15"/>
        <Setter Property="Height" Value="15"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentCyanBrush}"/>
        <Setter Property="BorderThickness" Value="2,2,0,0"/>
        <Setter Property="Opacity" Value="0.8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#42A5F5" BlurRadius="3" ShadowDepth="0" Opacity="0.6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 科技感扫描线样式 -->
    <Style x:Key="TechScanLineStyle" TargetType="Border">
        <Setter Property="Height" Value="2"/>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="Transparent" Offset="0"/>
                    <GradientStop Color="#42A5F5" Offset="0.3"/>
                    <GradientStop Color="#29B6F6" Offset="0.5"/>
                    <GradientStop Color="#42A5F5" Offset="0.7"/>
                    <GradientStop Color="Transparent" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Opacity" Value="0.6"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#42A5F5" BlurRadius="4" ShadowDepth="0" Opacity="0.8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 额外的颜色和画刷资源 -->
    <Color x:Key="AccentGreenColor">#4CAF50</Color>
    <SolidColorBrush x:Key="AccentGreenBrush" Color="{StaticResource AccentGreenColor}"/>

    <!-- 红色强调色 -->
    <Color x:Key="AccentRedColor">#F44336</Color>
    <SolidColorBrush x:Key="AccentRedBrush" Color="{StaticResource AccentRedColor}"/>

    <!-- 黄色强调色 -->
    <Color x:Key="AccentYellowColor">#FFC107</Color>
    <SolidColorBrush x:Key="AccentYellowBrush" Color="{StaticResource AccentYellowColor}"/>

    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="BorderPrimaryBrush" Color="#4FC3F7"/>
    <SolidColorBrush x:Key="BorderSecondaryBrush" Color="#2A4A73"/>

    <!-- 文本画刷 -->
    <SolidColorBrush x:Key="TextMutedBrush" Color="#78909C"/>

    <!-- 主标题样式 -->
    <Style x:Key="MainTitleStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextAccentBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource GlowColor}" BlurRadius="3" ShadowDepth="0" Opacity="0.8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 现代进度条样式 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource AccentCyanBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderBrush="{StaticResource BorderPrimaryBrush}"
                            BorderThickness="1">
                        <Grid>
                            <Rectangle Name="PART_Track" Fill="{TemplateBinding Background}" RadiusX="4" RadiusY="4"/>
                            <Rectangle Name="PART_Indicator"
                                      Fill="{TemplateBinding Foreground}"
                                      RadiusX="4" RadiusY="4"
                                      HorizontalAlignment="Left">
                                <Rectangle.Effect>
                                    <DropShadowEffect Color="{StaticResource GlowColor}" BlurRadius="6" ShadowDepth="0" Opacity="0.8"/>
                                </Rectangle.Effect>
                            </Rectangle>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 现代按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryMediumBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextAccentBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentCyanBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <Border.Effect>
                            <DropShadowEffect Color="{StaticResource GlowColor}" BlurRadius="4" ShadowDepth="0" Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentCyanBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
