<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引入颜色资源 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- 现代化卡片样式 -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardGradientBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderPrimaryBrush}"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource ShadowColor}" 
                                Direction="315" 
                                ShadowDepth="8" 
                                Opacity="0.4" 
                                BlurRadius="12"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="{StaticResource GlowColor}" 
                                        Direction="315" 
                                        ShadowDepth="8" 
                                        Opacity="0.6" 
                                        BlurRadius="16"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 现代化标题栏样式 -->
    <Style x:Key="ModernHeaderStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource HeaderGradientBrush}"/>
        <Setter Property="CornerRadius" Value="12,12,0,0"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderSecondaryBrush}"/>
    </Style>

    <!-- 标题文字样式 -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Left"/>
        <Setter Property="Margin" Value="16,0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource ShadowColor}" 
                                Direction="270" 
                                ShadowDepth="1" 
                                Opacity="0.8" 
                                BlurRadius="2"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主标题样式 -->
    <Style x:Key="MainTitleStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource AccentCyanBrush}"/>
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource GlowColor}" 
                                Direction="270" 
                                ShadowDepth="2" 
                                Opacity="0.6" 
                                BlurRadius="4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="10"/>
        <Setter Property="Height" Value="10"/>
        <Setter Property="HorizontalAlignment" Value="Right"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,16,0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource GlowColor}" 
                                Direction="0" 
                                ShadowDepth="0" 
                                Opacity="0.8" 
                                BlurRadius="6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 内容区域样式 -->
    <Style x:Key="ContentAreaStyle" TargetType="Grid">
        <Setter Property="Margin" Value="12"/>
    </Style>

    <!-- 装饰线条样式 -->
    <Style x:Key="DecorativeLineStyle" TargetType="Rectangle">
        <Setter Property="Height" Value="2"/>
        <Setter Property="Fill" Value="{StaticResource AccentCyanBrush}"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="Opacity" Value="0.6"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource GlowColor}" 
                                Direction="0" 
                                ShadowDepth="0" 
                                Opacity="0.8" 
                                BlurRadius="4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="{StaticResource ContentBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource AccentCyanBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4" 
                            BorderThickness="1" 
                            BorderBrush="{StaticResource BorderPrimaryBrush}">
                        <Grid>
                            <Rectangle Name="PART_Track" Fill="{TemplateBinding Background}"/>
                            <Rectangle Name="PART_Indicator" 
                                     Fill="{TemplateBinding Foreground}" 
                                     HorizontalAlignment="Left"
                                     CornerRadius="4"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource CardGradientBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderAccentBrush}"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="{StaticResource ShadowColor}" 
                                            Direction="315" 
                                            ShadowDepth="4" 
                                            Opacity="0.3" 
                                            BlurRadius="8"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentCyanBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource MainBackgroundBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource AccentGreenBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
