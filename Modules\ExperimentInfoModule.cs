using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using MainDispaly.Models;
using MainDispaly.Services;
using MainDispaly.ViewModels;

namespace MainDispaly.Modules
{
    /// <summary>
    /// 实验信息管理模块
    /// </summary>
    public class ExperimentInfoModule : BaseViewModel
    {
        private readonly DataService _dataService;
        private ExperimentData _currentExperiment;
        private DispatcherTimer _experimentTimer;
        private DateTime _experimentStartTime;
        private TimeSpan _elapsedTime;
        private bool _isExperimentRunning;

        /// <summary>
        /// 当前实验数据
        /// </summary>
        public ExperimentData CurrentExperiment
        {
            get => _currentExperiment;
            set => SetProperty(ref _currentExperiment, value);
        }

        /// <summary>
        /// 实验已运行时间
        /// </summary>
        public TimeSpan ElapsedTime
        {
            get => _elapsedTime;
            set => SetProperty(ref _elapsedTime, value);
        }

        /// <summary>
        /// 是否正在进行实验
        /// </summary>
        public bool IsExperimentRunning
        {
            get => _isExperimentRunning;
            set => SetProperty(ref _isExperimentRunning, value);
        }

        /// <summary>
        /// 实验进度百分比
        /// </summary>
        public double ExperimentProgress
        {
            get
            {
                if (CurrentExperiment?.EstimatedDuration > 0 && ElapsedTime.TotalMinutes > 0)
                {
                    return Math.Min(100, (ElapsedTime.TotalMinutes / CurrentExperiment.EstimatedDuration) * 100);
                }
                return 0;
            }
        }

        /// <summary>
        /// 预计剩余时间
        /// </summary>
        public TimeSpan EstimatedRemainingTime
        {
            get
            {
                if (CurrentExperiment?.EstimatedDuration > 0 && ElapsedTime.TotalMinutes > 0)
                {
                    var remainingMinutes = Math.Max(0, CurrentExperiment.EstimatedDuration - ElapsedTime.TotalMinutes);
                    return TimeSpan.FromMinutes(remainingMinutes);
                }
                return TimeSpan.Zero;
            }
        }

        /// <summary>
        /// 实验状态变化事件
        /// </summary>
        public event EventHandler<ExperimentStatusChangedEventArgs> ExperimentStatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataService">数据服务</param>
        public ExperimentInfoModule(DataService dataService)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            
            InitializeExperiment();
            InitializeTimer();
            
            LogMessage("实验信息模块初始化完成");
        }

        /// <summary>
        /// 初始化实验数据
        /// </summary>
        private void InitializeExperiment()
        {
            CurrentExperiment = new ExperimentData();
            CurrentExperiment.PropertyChanged += CurrentExperiment_PropertyChanged;
            
            ElapsedTime = TimeSpan.Zero;
            IsExperimentRunning = false;
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _experimentTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _experimentTimer.Tick += ExperimentTimer_Tick;
        }

        /// <summary>
        /// 创建新实验
        /// </summary>
        /// <param name="experimentUnit">实验单位</param>
        /// <param name="experimentName">实验名称</param>
        /// <param name="experimentPurpose">实验目的</param>
        /// <param name="estimatedDuration">预计时长（分钟）</param>
        public void CreateNewExperiment(string experimentUnit, string experimentName, string experimentPurpose, int estimatedDuration)
        {
            try
            {
                LogMessage("创建新实验");
                
                CurrentExperiment = new ExperimentData
                {
                    ExperimentUnit = experimentUnit,
                    ExperimentNumber = GenerateExperimentNumber(),
                    ExperimentName = experimentName,
                    ExperimentPurpose = experimentPurpose,
                    EstimatedDuration = estimatedDuration,
                    Status = ExperimentStatus.Ready
                };
                
                CurrentExperiment.PropertyChanged += CurrentExperiment_PropertyChanged;
                
                LogMessage($"新实验创建完成: {CurrentExperiment.ExperimentNumber}");
            }
            catch (Exception ex)
            {
                LogMessage($"创建新实验失败: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 开始实验
        /// </summary>
        public async Task<bool> StartExperimentAsync()
        {
            try
            {
                if (CurrentExperiment.Status != ExperimentStatus.Ready)
                {
                    LogMessage("实验状态不正确，无法开始", LogLevel.Warning);
                    return false;
                }

                LogMessage("开始实验");
                
                CurrentExperiment.Status = ExperimentStatus.Running;
                CurrentExperiment.StartTime = DateTime.Now;
                _experimentStartTime = DateTime.Now;
                
                IsExperimentRunning = true;
                _experimentTimer.Start();
                
                // 保存实验开始状态
                await _dataService.SaveExperimentDataAsync(CurrentExperiment);
                
                OnExperimentStatusChanged(new ExperimentStatusChangedEventArgs(ExperimentStatus.Running));
                
                LogMessage($"实验 {CurrentExperiment.ExperimentNumber} 已开始");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"开始实验失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 暂停实验
        /// </summary>
        public async Task<bool> PauseExperimentAsync()
        {
            try
            {
                if (CurrentExperiment.Status != ExperimentStatus.Running)
                {
                    LogMessage("实验未在运行，无法暂停", LogLevel.Warning);
                    return false;
                }

                LogMessage("暂停实验");
                
                CurrentExperiment.Status = ExperimentStatus.Paused;
                _experimentTimer.Stop();
                
                // 保存暂停状态
                await _dataService.SaveExperimentDataAsync(CurrentExperiment);
                
                OnExperimentStatusChanged(new ExperimentStatusChangedEventArgs(ExperimentStatus.Paused));
                
                LogMessage($"实验 {CurrentExperiment.ExperimentNumber} 已暂停");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"暂停实验失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 恢复实验
        /// </summary>
        public async Task<bool> ResumeExperimentAsync()
        {
            try
            {
                if (CurrentExperiment.Status != ExperimentStatus.Paused)
                {
                    LogMessage("实验未暂停，无法恢复", LogLevel.Warning);
                    return false;
                }

                LogMessage("恢复实验");
                
                CurrentExperiment.Status = ExperimentStatus.Running;
                _experimentTimer.Start();
                
                OnExperimentStatusChanged(new ExperimentStatusChangedEventArgs(ExperimentStatus.Running));
                
                LogMessage($"实验 {CurrentExperiment.ExperimentNumber} 已恢复");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"恢复实验失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 完成实验
        /// </summary>
        public async Task<bool> CompleteExperimentAsync()
        {
            try
            {
                if (CurrentExperiment.Status != ExperimentStatus.Running && CurrentExperiment.Status != ExperimentStatus.Paused)
                {
                    LogMessage("实验状态不正确，无法完成", LogLevel.Warning);
                    return false;
                }

                LogMessage("完成实验");
                
                CurrentExperiment.Status = ExperimentStatus.Completed;
                CurrentExperiment.EndTime = DateTime.Now;
                
                IsExperimentRunning = false;
                _experimentTimer.Stop();
                
                // 保存完成状态
                await _dataService.SaveExperimentDataAsync(CurrentExperiment);
                
                OnExperimentStatusChanged(new ExperimentStatusChangedEventArgs(ExperimentStatus.Completed));
                
                LogMessage($"实验 {CurrentExperiment.ExperimentNumber} 已完成，总用时: {CurrentExperiment.Duration}");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"完成实验失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 终止实验（出现错误时）
        /// </summary>
        public async Task<bool> AbortExperimentAsync(string errorMessage)
        {
            try
            {
                LogMessage($"终止实验: {errorMessage}", LogLevel.Error);
                
                CurrentExperiment.Status = ExperimentStatus.Error;
                CurrentExperiment.StatusMessage = errorMessage;
                CurrentExperiment.EndTime = DateTime.Now;
                
                IsExperimentRunning = false;
                _experimentTimer.Stop();
                
                // 保存错误状态
                await _dataService.SaveExperimentDataAsync(CurrentExperiment);
                
                OnExperimentStatusChanged(new ExperimentStatusChangedEventArgs(ExperimentStatus.Error));
                
                LogMessage($"实验 {CurrentExperiment.ExperimentNumber} 已终止");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"终止实验失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 生成实验编号
        /// </summary>
        private string GenerateExperimentNumber()
        {
            return $"EXP-{DateTime.Now:yyyy}-{DateTime.Now:MMdd}-{DateTime.Now:HHmmss}";
        }

        /// <summary>
        /// 实验定时器事件
        /// </summary>
        private void ExperimentTimer_Tick(object sender, EventArgs e)
        {
            if (IsExperimentRunning && CurrentExperiment.Status == ExperimentStatus.Running)
            {
                ElapsedTime = DateTime.Now - _experimentStartTime;
                OnPropertyChanged(nameof(ExperimentProgress));
                OnPropertyChanged(nameof(EstimatedRemainingTime));
                
                // 检查是否超过预计时间
                if (ElapsedTime.TotalMinutes > CurrentExperiment.EstimatedDuration * 1.2) // 超过预计时间20%
                {
                    LogMessage("实验时间超过预计时间", LogLevel.Warning);
                }
            }
        }

        /// <summary>
        /// 实验数据属性变化事件
        /// </summary>
        private void CurrentExperiment_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ExperimentData.Status))
            {
                LogMessage($"实验状态变化: {CurrentExperiment.StatusText}");
            }
        }

        /// <summary>
        /// 触发实验状态变化事件
        /// </summary>
        private void OnExperimentStatusChanged(ExperimentStatusChangedEventArgs e)
        {
            ExperimentStatusChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _experimentTimer?.Stop();
            
            if (CurrentExperiment != null)
            {
                CurrentExperiment.PropertyChanged -= CurrentExperiment_PropertyChanged;
            }
            
            LogMessage("实验信息模块资源已释放");
        }
    }

    /// <summary>
    /// 实验状态变化事件参数
    /// </summary>
    public class ExperimentStatusChangedEventArgs : EventArgs
    {
        public ExperimentStatus Status { get; private set; }

        public ExperimentStatusChangedEventArgs(ExperimentStatus status)
        {
            Status = status;
        }
    }
}
