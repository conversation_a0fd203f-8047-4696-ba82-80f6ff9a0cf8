using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Threading;
using MainDispaly.Models;
using MainDispaly.Services;
using MainDispaly.ViewModels;

namespace MainDispaly.Modules
{
    /// <summary>
    /// 视频监控模块
    /// </summary>
    public class VideoMonitoringModule : BaseViewModel
    {
        private readonly VideoService _videoService;
        private readonly DispatcherTimer _monitoringTimer;
        
        private ObservableCollection<VideoSource> _cameras;
        private bool _isMonitoring;
        private string _monitoringStatus;
        private int _activeCameraCount;

        /// <summary>
        /// 摄像头列表
        /// </summary>
        public ObservableCollection<VideoSource> Cameras
        {
            get => _cameras;
            set => SetProperty(ref _cameras, value);
        }

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring
        {
            get => _isMonitoring;
            set => SetProperty(ref _isMonitoring, value);
        }

        /// <summary>
        /// 监控状态
        /// </summary>
        public string MonitoringStatus
        {
            get => _monitoringStatus;
            set => SetProperty(ref _monitoringStatus, value);
        }

        /// <summary>
        /// 活跃摄像头数量
        /// </summary>
        public int ActiveCameraCount
        {
            get => _activeCameraCount;
            set => SetProperty(ref _activeCameraCount, value);
        }

        /// <summary>
        /// 摄像头状态变化事件
        /// </summary>
        public event EventHandler<CameraStatusChangedEventArgs> CameraStatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="videoService">视频服务</param>
        public VideoMonitoringModule(VideoService videoService)
        {
            _videoService = videoService ?? throw new ArgumentNullException(nameof(videoService));
            
            InitializeCameras();
            InitializeTimer();
            
            LogMessage("视频监控模块初始化完成");
        }

        /// <summary>
        /// 初始化摄像头
        /// </summary>
        private void InitializeCameras()
        {
            Cameras = new ObservableCollection<VideoSource>
            {
                new VideoSource("摄像头 #1", "rtsp://192.168.1.101/stream", VideoSourceType.Network),
                new VideoSource("摄像头 #2", "rtsp://192.168.1.102/stream", VideoSourceType.Network),
                new VideoSource("摄像头 #3", "rtsp://192.168.1.103/stream", VideoSourceType.Network),
                new VideoSource("摄像头 #4", "rtsp://192.168.1.104/stream", VideoSourceType.Network)
            };

            // 注册摄像头到视频服务
            foreach (var camera in Cameras)
            {
                _videoService.RegisterVideoSource(camera);
                camera.PropertyChanged += Camera_PropertyChanged;
            }

            UpdateStatus();
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _monitoringTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // 每5秒检查一次摄像头状态
            };
            _monitoringTimer.Tick += MonitoringTimer_Tick;
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public async Task<bool> StartMonitoringAsync()
        {
            try
            {
                LogMessage("开始启动视频监控");
                
                IsMonitoring = true;
                MonitoringStatus = "正在启动监控...";

                // 连接所有摄像头
                var connectionTasks = Cameras.Select(camera => ConnectCameraAsync(camera.Name));
                var results = await Task.WhenAll(connectionTasks);

                // 启动监控定时器
                _monitoringTimer.Start();

                var successCount = results.Count(r => r);
                MonitoringStatus = $"监控已启动，{successCount}/{Cameras.Count} 个摄像头连接成功";
                
                LogMessage($"视频监控启动完成，成功连接 {successCount} 个摄像头");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                LogMessage($"启动视频监控失败: {ex.Message}", LogLevel.Error);
                IsMonitoring = false;
                MonitoringStatus = "监控启动失败";
                return false;
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public async Task StopMonitoringAsync()
        {
            try
            {
                LogMessage("正在停止视频监控");
                
                _monitoringTimer.Stop();
                IsMonitoring = false;
                MonitoringStatus = "正在停止监控...";

                // 断开所有摄像头
                var disconnectionTasks = Cameras.Select(camera => DisconnectCameraAsync(camera.Name));
                await Task.WhenAll(disconnectionTasks);

                MonitoringStatus = "监控已停止";
                LogMessage("视频监控已停止");
            }
            catch (Exception ex)
            {
                LogMessage($"停止视频监控失败: {ex.Message}", LogLevel.Error);
                MonitoringStatus = "停止监控失败";
            }
        }

        /// <summary>
        /// 连接摄像头
        /// </summary>
        /// <param name="cameraName">摄像头名称</param>
        public async Task<bool> ConnectCameraAsync(string cameraName)
        {
            try
            {
                var camera = Cameras.FirstOrDefault(c => c.Name == cameraName);
                if (camera == null)
                    return false;

                LogMessage($"正在连接摄像头: {cameraName}");
                
                var result = await _videoService.ConnectVideoSourceAsync(cameraName);
                
                if (result)
                {
                    LogMessage($"摄像头 {cameraName} 连接成功");
                    await _videoService.StartPlaybackAsync(cameraName);
                }
                else
                {
                    LogMessage($"摄像头 {cameraName} 连接失败", LogLevel.Warning);
                }

                return result;
            }
            catch (Exception ex)
            {
                LogMessage($"连接摄像头 {cameraName} 时发生错误: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 断开摄像头
        /// </summary>
        /// <param name="cameraName">摄像头名称</param>
        public async Task<bool> DisconnectCameraAsync(string cameraName)
        {
            try
            {
                LogMessage($"正在断开摄像头: {cameraName}");
                
                await _videoService.StopPlaybackAsync(cameraName);
                var result = await _videoService.DisconnectVideoSourceAsync(cameraName);
                
                if (result)
                {
                    LogMessage($"摄像头 {cameraName} 已断开");
                }

                return result;
            }
            catch (Exception ex)
            {
                LogMessage($"断开摄像头 {cameraName} 时发生错误: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// 重新连接摄像头
        /// </summary>
        /// <param name="cameraName">摄像头名称</param>
        public async Task<bool> ReconnectCameraAsync(string cameraName)
        {
            LogMessage($"正在重新连接摄像头: {cameraName}");

            await DisconnectCameraAsync(cameraName);
            await Task.Delay(1000); // 等待1秒
            return await ConnectCameraAsync(cameraName);
        }

        /// <summary>
        /// 重新连接所有摄像头
        /// </summary>
        public async Task ReconnectAllCamerasAsync()
        {
            LogMessage("正在重新连接所有摄像头");
            
            var reconnectionTasks = Cameras.Select(camera => ReconnectCameraAsync(camera.Name));
            await Task.WhenAll(reconnectionTasks);
            
            LogMessage("所有摄像头重新连接完成");
        }

        /// <summary>
        /// 监控定时器事件
        /// </summary>
        private async void MonitoringTimer_Tick(object sender, EventArgs e)
        {
            await CheckCameraStatusAsync();
        }

        /// <summary>
        /// 检查摄像头状态
        /// </summary>
        private async Task CheckCameraStatusAsync()
        {
            try
            {
                foreach (var camera in Cameras)
                {
                    // 检查摄像头是否需要重新连接
                    if (camera.Status == VideoStatus.Error || camera.Status == VideoStatus.Disconnected)
                    {
                        LogMessage($"检测到摄像头 {camera.Name} 状态异常，尝试重新连接", LogLevel.Warning);
                        await ReconnectCameraAsync(camera.Name);
                    }
                }

                UpdateStatus();
            }
            catch (Exception ex)
            {
                LogMessage($"检查摄像头状态时发生错误: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        private void UpdateStatus()
        {
            ActiveCameraCount = Cameras.Count(c => c.IsOnline);
            
            if (!IsMonitoring)
                return;

            if (ActiveCameraCount == Cameras.Count)
            {
                MonitoringStatus = "所有摄像头正常运行";
            }
            else if (ActiveCameraCount > 0)
            {
                MonitoringStatus = $"{ActiveCameraCount}/{Cameras.Count} 个摄像头正常运行";
            }
            else
            {
                MonitoringStatus = "所有摄像头离线";
            }
        }

        /// <summary>
        /// 摄像头属性变化事件
        /// </summary>
        private void Camera_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(VideoSource.Status))
            {
                var camera = sender as VideoSource;
                UpdateStatus();
                CameraStatusChanged?.Invoke(this, new CameraStatusChangedEventArgs(camera));
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _monitoringTimer?.Stop();
            
            foreach (var camera in Cameras)
            {
                camera.PropertyChanged -= Camera_PropertyChanged;
                _videoService.UnregisterVideoSource(camera.Name);
            }
            
            LogMessage("视频监控模块资源已释放");
        }
    }

    /// <summary>
    /// 摄像头状态变化事件参数
    /// </summary>
    public class CameraStatusChangedEventArgs : EventArgs
    {
        public VideoSource Camera { get; }

        public CameraStatusChangedEventArgs(VideoSource camera)
        {
            Camera = camera;
        }
    }
}
