<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 主色调 - 深色科技感 -->
    <Color x:Key="PrimaryDarkColor">#0A1628</Color>
    <Color x:Key="PrimaryMediumColor">#1E3A8A</Color>
    <Color x:Key="PrimaryLightColor">#3B82F6</Color>
    
    <!-- 背景色 -->
    <Color x:Key="MainBackgroundColor">#0A0A0A</Color>
    <Color x:Key="CardBackgroundColor">#CC1A1A1A</Color>
    <Color x:Key="HeaderBackgroundColor">#CC2D2D2D</Color>
    <Color x:Key="ContentBackgroundColor">#CC262626</Color>
    <Color x:Key="TechBackgroundColor">#CC0F1419</Color>
    <Color x:Key="GridLineColor">#1A3B82F6</Color>
    <Color x:Key="TechAccentColor">#4400D4FF</Color>
    
    <!-- 强调色 -->
    <Color x:Key="AccentCyanColor">#00D4FF</Color>
    <Color x:Key="AccentGreenColor">#00FF96</Color>
    <Color x:Key="AccentOrangeColor">#FF9500</Color>
    <Color x:Key="AccentRedColor">#FF6B6B</Color>
    <Color x:Key="AccentPurpleColor">#8B5CF6</Color>
    <Color x:Key="AccentYellowColor">#FCD34D</Color>
    
    <!-- 状态色 -->
    <Color x:Key="SuccessColor">#10B981</Color>
    <Color x:Key="WarningColor">#F59E0B</Color>
    <Color x:Key="ErrorColor">#EF4444</Color>
    <Color x:Key="InfoColor">#3B82F6</Color>
    
    <!-- 文字色 -->
    <Color x:Key="TextPrimaryColor">#FFFFFF</Color>
    <Color x:Key="TextSecondaryColor">#D1D5DB</Color>
    <Color x:Key="TextMutedColor">#9CA3AF</Color>
    <Color x:Key="TextDisabledColor">#6B7280</Color>
    
    <!-- 边框色 -->
    <Color x:Key="BorderPrimaryColor">#374151</Color>
    <Color x:Key="BorderSecondaryColor">#4B5563</Color>
    <Color x:Key="BorderAccentColor">#00D4FF</Color>
    
    <!-- 阴影色 -->
    <Color x:Key="ShadowColor">#000000</Color>
    <Color x:Key="GlowColor">#00D4FF</Color>
    
    <!-- 渐变色 -->
    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="{StaticResource PrimaryDarkColor}" Offset="0"/>
        <GradientStop Color="{StaticResource PrimaryMediumColor}" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="CardGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#CC1A1A1A" Offset="0"/>
        <GradientStop Color="#CC2D2D2D" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#CC2D2D2D" Offset="0"/>
        <GradientStop Color="#CC1A1A1A" Offset="1"/>
    </LinearGradientBrush>
    
    <RadialGradientBrush x:Key="GlowBrush" Center="0.5,0.5" RadiusX="1" RadiusY="1">
        <GradientStop Color="#4400D4FF" Offset="0"/>
        <GradientStop Color="#0000D4FF" Offset="1"/>
    </RadialGradientBrush>

    <!-- 科技感背景渐变 -->
    <LinearGradientBrush x:Key="TechBackgroundBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#CC0F1419" Offset="0"/>
        <GradientStop Color="#CC1A1A1A" Offset="0.3"/>
        <GradientStop Color="#CC0A1628" Offset="0.7"/>
        <GradientStop Color="#CC1A1A1A" Offset="1"/>
    </LinearGradientBrush>

    <!-- 科技感网格背景 -->
    <LinearGradientBrush x:Key="GridBackgroundBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#CC0A1628" Offset="0"/>
        <GradientStop Color="#CC1E3A8A" Offset="0.5"/>
        <GradientStop Color="#CC0F1419" Offset="1"/>
    </LinearGradientBrush>

    <!-- 发光边框渐变 -->
    <LinearGradientBrush x:Key="GlowBorderBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="#0000D4FF" Offset="0"/>
        <GradientStop Color="#8800D4FF" Offset="0.5"/>
        <GradientStop Color="#0000D4FF" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- 实体画刷 -->
    <SolidColorBrush x:Key="MainBackgroundBrush" Color="{StaticResource MainBackgroundColor}"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="{StaticResource CardBackgroundColor}"/>
    <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="{StaticResource HeaderBackgroundColor}"/>
    <SolidColorBrush x:Key="ContentBackgroundBrush" Color="{StaticResource ContentBackgroundColor}"/>
    
    <SolidColorBrush x:Key="AccentCyanBrush" Color="{StaticResource AccentCyanColor}"/>
    <SolidColorBrush x:Key="AccentGreenBrush" Color="{StaticResource AccentGreenColor}"/>
    <SolidColorBrush x:Key="AccentOrangeBrush" Color="{StaticResource AccentOrangeColor}"/>
    <SolidColorBrush x:Key="AccentRedBrush" Color="{StaticResource AccentRedColor}"/>
    <SolidColorBrush x:Key="AccentPurpleBrush" Color="{StaticResource AccentPurpleColor}"/>
    <SolidColorBrush x:Key="AccentYellowBrush" Color="{StaticResource AccentYellowColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextMutedBrush" Color="{StaticResource TextMutedColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    
    <SolidColorBrush x:Key="BorderPrimaryBrush" Color="{StaticResource BorderPrimaryColor}"/>
    <SolidColorBrush x:Key="BorderSecondaryBrush" Color="{StaticResource BorderSecondaryColor}"/>
    <SolidColorBrush x:Key="BorderAccentBrush" Color="{StaticResource BorderAccentColor}"/>

    <SolidColorBrush x:Key="TechBackgroundSolidBrush" Color="{StaticResource TechBackgroundColor}"/>
    <SolidColorBrush x:Key="GridLineBrush" Color="{StaticResource GridLineColor}"/>
    <SolidColorBrush x:Key="TechAccentBrush" Color="{StaticResource TechAccentColor}"/>

</ResourceDictionary>
