using System;
using System.ComponentModel;

namespace MainDispaly.Models
{
    /// <summary>
    /// 环境数据模型
    /// </summary>
    public class EnvironmentData : INotifyPropertyChanged
    {
        private double _temperature;
        private double _humidity;
        private double _pressure;
        private double _voltage;
        private DateTime _timestamp;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 温度（摄氏度）
        /// </summary>
        public double Temperature
        {
            get => _temperature;
            set
            {
                _temperature = value;
                OnPropertyChanged(nameof(Temperature));
                OnPropertyChanged(nameof(TemperatureStatus));
            }
        }

        /// <summary>
        /// 湿度（百分比）
        /// </summary>
        public double Humidity
        {
            get => _humidity;
            set
            {
                _humidity = value;
                OnPropertyChanged(nameof(Humidity));
                OnPropertyChanged(nameof(HumidityStatus));
            }
        }

        /// <summary>
        /// 气压（帕斯卡）
        /// </summary>
        public double Pressure
        {
            get => _pressure;
            set
            {
                _pressure = value;
                OnPropertyChanged(nameof(Pressure));
                OnPropertyChanged(nameof(PressureStatus));
            }
        }

        /// <summary>
        /// 电压（伏特）
        /// </summary>
        public double Voltage
        {
            get => _voltage;
            set
            {
                _voltage = value;
                OnPropertyChanged(nameof(Voltage));
                OnPropertyChanged(nameof(VoltageStatus));
            }
        }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                _timestamp = value;
                OnPropertyChanged(nameof(Timestamp));
            }
        }

        /// <summary>
        /// 温度状态
        /// </summary>
        public EnvironmentStatus TemperatureStatus
        {
            get
            {
                if (Temperature < 15 || Temperature > 35)
                    return EnvironmentStatus.Warning;
                if (Temperature < 10 || Temperature > 40)
                    return EnvironmentStatus.Error;
                return EnvironmentStatus.Normal;
            }
        }

        /// <summary>
        /// 湿度状态
        /// </summary>
        public EnvironmentStatus HumidityStatus
        {
            get
            {
                if (Humidity < 30 || Humidity > 70)
                    return EnvironmentStatus.Warning;
                if (Humidity < 20 || Humidity > 80)
                    return EnvironmentStatus.Error;
                return EnvironmentStatus.Normal;
            }
        }

        /// <summary>
        /// 气压状态
        /// </summary>
        public EnvironmentStatus PressureStatus
        {
            get
            {
                if (Pressure < 95000 || Pressure > 105000)
                    return EnvironmentStatus.Warning;
                if (Pressure < 90000 || Pressure > 110000)
                    return EnvironmentStatus.Error;
                return EnvironmentStatus.Normal;
            }
        }

        /// <summary>
        /// 电压状态
        /// </summary>
        public EnvironmentStatus VoltageStatus
        {
            get
            {
                if (Voltage < 210 || Voltage > 230)
                    return EnvironmentStatus.Warning;
                if (Voltage < 200 || Voltage > 240)
                    return EnvironmentStatus.Error;
                return EnvironmentStatus.Normal;
            }
        }

        /// <summary>
        /// 整体环境状态
        /// </summary>
        public EnvironmentStatus OverallStatus
        {
            get
            {
                var statuses = new[] { TemperatureStatus, HumidityStatus, PressureStatus, VoltageStatus };
                
                if (Array.Exists(statuses, s => s == EnvironmentStatus.Error))
                    return EnvironmentStatus.Error;
                
                if (Array.Exists(statuses, s => s == EnvironmentStatus.Warning))
                    return EnvironmentStatus.Warning;
                
                return EnvironmentStatus.Normal;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public EnvironmentData()
        {
            // 设置默认值
            Temperature = 23.5;
            Humidity = 45.0;
            Pressure = 101325.0; // 标准大气压
            Voltage = 220.0;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 更新所有环境数据
        /// </summary>
        /// <param name="temperature">温度</param>
        /// <param name="humidity">湿度</param>
        /// <param name="pressure">气压</param>
        /// <param name="voltage">电压</param>
        public void UpdateAll(double temperature, double humidity, double pressure, double voltage)
        {
            Temperature = temperature;
            Humidity = humidity;
            Pressure = pressure;
            Voltage = voltage;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 生成随机环境数据（用于模拟）
        /// </summary>
        public void GenerateRandomData()
        {
            var random = new Random();
            
            // 在正常范围内生成随机数据
            Temperature = 20 + random.NextDouble() * 10; // 20-30°C
            Humidity = 40 + random.NextDouble() * 20;     // 40-60%
            Pressure = 100000 + random.NextDouble() * 2000; // 100-102 kPa
            Voltage = 215 + random.NextDouble() * 10;     // 215-225V
            Timestamp = DateTime.Now;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 环境状态枚举
    /// </summary>
    public enum EnvironmentStatus
    {
        Normal,     // 正常
        Warning,    // 警告
        Error       // 错误
    }
}
