﻿<Window x:Class="MainDispaly.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MainDispaly"
        mc:Ignorable="d"
        Title="电磁辐射实验大屏展示系统"
        Height="1080"
        Width="1920"
        WindowState="Maximized"
        WindowStyle="None"
        Background="#FF0A0A0A"
        ResizeMode="CanResizeWithGrip">

    <!-- 主布局Grid：3纵2横5区域布局 -->
    <Grid>
        <!-- 定义列：左列(1*) 中列(2*) 右列(1*) -->
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>

        <!-- 定义行：上行和下行 -->
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>

        <!-- 左上区域：视频监控 -->
        <Border Grid.Column="0"
                Grid.Row="0"
                Background="#FF1A1A1A"
                CornerRadius="10"
                Margin="10"
                BorderBrush="#FF333333"
                BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                  Direction="315"
                                  ShadowDepth="5"
                                  Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0"
                        Background="#FF2D2D2D"
                        CornerRadius="10,10,0,0"
                        Height="40">
                    <Grid>
                        <TextBlock Text="视频监控"
                                   Foreground="#FF00D4FF"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Left"
                                   Margin="15,0"/>
                        <Ellipse Width="8"
                                 Height="8"
                                 Fill="#FF00FF00"
                                 HorizontalAlignment="Right"
                                 VerticalAlignment="Center"
                                 Margin="0,0,15,0"/>
                    </Grid>
                </Border>

                <!-- 内容区域 -->
                <Grid Grid.Row="1"
                      Name="VideoMonitoringArea"
                      Margin="10">
                    <TextBlock Text="视频监控区域"
                               Foreground="#FF666666"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Grid>
            </Grid>
        </Border>

        <!-- 左下区域：安全联锁状态 -->
        <Border Grid.Column="0"
                Grid.Row="1"
                Background="#FF1A1A1A"
                CornerRadius="10"
                Margin="10"
                BorderBrush="#FF333333"
                BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                  Direction="315"
                                  ShadowDepth="5"
                                  Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0"
                        Background="#FF2D2D2D"
                        CornerRadius="10,10,0,0"
                        Height="40">
                    <Grid>
                        <TextBlock Text="安全联锁状态"
                                   Foreground="#FFFF9500"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Left"
                                   Margin="15,0"/>
                        <Ellipse Width="8"
                                 Height="8"
                                 Fill="#FFFF9500"
                                 HorizontalAlignment="Right"
                                 VerticalAlignment="Center"
                                 Margin="0,0,15,0"/>
                    </Grid>
                </Border>

                <!-- 内容区域 -->
                <Grid Grid.Row="1"
                      Name="SafetyInterlockArea"
                      Margin="10">
                    <TextBlock Text="安全联锁状态区域"
                               Foreground="#FF666666"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Grid>
            </Grid>
        </Border>

        <!-- 中部区域：3D展示和实验进度 -->
        <Border Grid.Column="1"
                Grid.Row="0"
                Grid.RowSpan="2"
                Background="#FF1A1A1A"
                CornerRadius="10"
                Margin="10"
                BorderBrush="#FF333333"
                BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                  Direction="315"
                                  ShadowDepth="5"
                                  Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0"
                        Background="#FF2D2D2D"
                        CornerRadius="10,10,0,0"
                        Height="50">
                    <Grid>
                        <TextBlock Text="电磁辐射实验系统"
                                   Foreground="#FF00D4FF"
                                   FontSize="24"
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Center"/>
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Margin="0,0,20,0">
                            <TextBlock Text="准备阶段"
                                       Foreground="#FF00FF00"
                                       FontSize="16"
                                       FontWeight="Bold"
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                            <Ellipse Width="12"
                                     Height="12"
                                     Fill="#FF00FF00"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 实验进度区域 -->
                <Border Grid.Row="1"
                        Background="#FF262626"
                        Height="80"
                        Margin="10,5">
                    <Grid Name="ExperimentProgressArea">
                        <TextBlock Text="实验进度和阶段信息区域"
                                   Foreground="#FF666666"
                                   FontSize="16"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                    </Grid>
                </Border>

                <!-- 3D展示区域 -->
                <Grid Grid.Row="2"
                      Name="Video3DArea"
                      Margin="10">
                    <Border Background="#FF0D0D0D"
                            CornerRadius="5"
                            BorderBrush="#FF444444"
                            BorderThickness="1">
                        <TextBlock Text="3D展示视频区域"
                                   Foreground="#FF666666"
                                   FontSize="18"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- 右上区域：实验内容信息 -->
        <Border Grid.Column="2"
                Grid.Row="0"
                Background="#FF1A1A1A"
                CornerRadius="10"
                Margin="10"
                BorderBrush="#FF333333"
                BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                  Direction="315"
                                  ShadowDepth="5"
                                  Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0"
                        Background="#FF2D2D2D"
                        CornerRadius="10,10,0,0"
                        Height="40">
                    <Grid>
                        <TextBlock Text="实验信息"
                                   Foreground="#FF00FF96"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Left"
                                   Margin="15,0"/>
                        <Ellipse Width="8"
                                 Height="8"
                                 Fill="#FF00FF96"
                                 HorizontalAlignment="Right"
                                 VerticalAlignment="Center"
                                 Margin="0,0,15,0"/>
                    </Grid>
                </Border>

                <!-- 内容区域 -->
                <Grid Grid.Row="1"
                      Name="ExperimentInfoArea"
                      Margin="10">
                    <TextBlock Text="实验内容信息区域"
                               Foreground="#FF666666"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Grid>
            </Grid>
        </Border>

        <!-- 右下区域：装置状态 -->
        <Border Grid.Column="2"
                Grid.Row="1"
                Background="#FF1A1A1A"
                CornerRadius="10"
                Margin="10"
                BorderBrush="#FF333333"
                BorderThickness="1">
            <Border.Effect>
                <DropShadowEffect Color="Black"
                                  Direction="315"
                                  ShadowDepth="5"
                                  Opacity="0.3"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0"
                        Background="#FF2D2D2D"
                        CornerRadius="10,10,0,0"
                        Height="40">
                    <Grid>
                        <TextBlock Text="装置状态"
                                   Foreground="#FFFF6B6B"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Left"
                                   Margin="15,0"/>
                        <Ellipse Width="8"
                                 Height="8"
                                 Fill="#FFFF6B6B"
                                 HorizontalAlignment="Right"
                                 VerticalAlignment="Center"
                                 Margin="0,0,15,0"/>
                    </Grid>
                </Border>

                <!-- 内容区域 -->
                <Grid Grid.Row="1"
                      Name="DeviceStatusArea"
                      Margin="10">
                    <TextBlock Text="装置状态监控区域"
                               Foreground="#FF666666"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Grid>
            </Grid>
        </Border>

        <!-- 顶部状态栏（可选，用于显示系统时间等信息） -->
        <Border Grid.Column="0"
                Grid.ColumnSpan="3"
                VerticalAlignment="Top"
                Height="30"
                Background="#AA000000"
                Margin="0">
            <Grid>
                <TextBlock Text="电磁辐射实验大屏展示系统 v1.0"
                           Foreground="#FF888888"
                           FontSize="12"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Left"
                           Margin="20,0"/>
                <TextBlock Name="SystemTimeDisplay"
                           Text="2025-01-16 14:30:00"
                           Foreground="#FF888888"
                           FontSize="12"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Right"
                           Margin="0,0,20,0"/>
            </Grid>
        </Border>
    </Grid>
</Window>
