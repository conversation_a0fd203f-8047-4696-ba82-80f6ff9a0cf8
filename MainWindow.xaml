﻿<Window x:Class="MainDispaly.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MainDispaly"
        mc:Ignorable="d"
        Title="电磁辐射实验大屏展示系统"
        Height="1080"
        Width="1920"
        WindowState="Maximized"
        WindowStyle="None"
        ResizeMode="CanResizeWithGrip">

    <!-- 主窗口背景 - 支持背景图片 -->
    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0A1628" Offset="0"/>
            <GradientStop Color="#1E3A8A" Offset="0.5"/>
            <GradientStop Color="#0A0A0A" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>

    <!-- 主布局Grid：3纵2横5区域布局 -->
    <Grid>
        <!-- 定义列：左列(1*) 中列(2*) 右列(1*) -->
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*"/>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>

        <!-- 定义行：上行和下行 -->
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>

        <!-- 左上区域：视频监控 -->
        <Border Grid.Column="0"
                Grid.Row="0"
                Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                    <Grid>
                        <TextBlock Text="视频监控" Style="{StaticResource HeaderTextStyle}">
                            <TextBlock.Foreground>
                                <SolidColorBrush Color="{StaticResource AccentCyanColor}"/>
                            </TextBlock.Foreground>
                        </TextBlock>
                        <StackPanel Orientation="Horizontal"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"
                                   Margin="0,0,16,0">
                            <TextBlock Text="在线"
                                      Foreground="{StaticResource SuccessBrush}"
                                      FontSize="12"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <Ellipse Style="{StaticResource StatusIndicatorStyle}"
                                    Fill="{StaticResource SuccessBrush}"
                                    Margin="0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 装饰线条 -->
                <Rectangle Grid.Row="0"
                          VerticalAlignment="Bottom"
                          Style="{StaticResource DecorativeLineStyle}"
                          Margin="16,0"/>

                <!-- 内容区域 -->
                <Grid Grid.Row="1" Name="VideoMonitoringArea" Style="{StaticResource ContentAreaStyle}">
                    <!-- 视频网格布局 -->
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 视频窗口占位符 -->
                        <Border Grid.Row="0" Grid.Column="0"
                               Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="6"
                               Margin="2"
                               BorderThickness="1"
                               BorderBrush="{StaticResource BorderPrimaryBrush}">
                            <TextBlock Text="摄像头 #1"
                                      Foreground="{StaticResource TextMutedBrush}"
                                      FontSize="12"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>

                        <Border Grid.Row="0" Grid.Column="1"
                               Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="6"
                               Margin="2"
                               BorderThickness="1"
                               BorderBrush="{StaticResource BorderPrimaryBrush}">
                            <TextBlock Text="摄像头 #2"
                                      Foreground="{StaticResource TextMutedBrush}"
                                      FontSize="12"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>

                        <Border Grid.Row="1" Grid.Column="0"
                               Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="6"
                               Margin="2"
                               BorderThickness="1"
                               BorderBrush="{StaticResource BorderPrimaryBrush}">
                            <TextBlock Text="摄像头 #3"
                                      Foreground="{StaticResource TextMutedBrush}"
                                      FontSize="12"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>

                        <Border Grid.Row="1" Grid.Column="1"
                               Background="{StaticResource ContentBackgroundBrush}"
                               CornerRadius="6"
                               Margin="2"
                               BorderThickness="1"
                               BorderBrush="{StaticResource BorderPrimaryBrush}">
                            <TextBlock Text="摄像头 #4"
                                      Foreground="{StaticResource TextMutedBrush}"
                                      FontSize="12"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Grid>
            </Grid>
        </Border>

        <!-- 左下区域：安全联锁状态 -->
        <Border Grid.Column="0"
                Grid.Row="1"
                Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                    <Grid>
                        <TextBlock Text="安全联锁状态" Style="{StaticResource HeaderTextStyle}">
                            <TextBlock.Foreground>
                                <SolidColorBrush Color="{StaticResource AccentOrangeColor}"/>
                            </TextBlock.Foreground>
                        </TextBlock>
                        <StackPanel Orientation="Horizontal"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"
                                   Margin="0,0,16,0">
                            <TextBlock Text="监控中"
                                      Foreground="{StaticResource WarningBrush}"
                                      FontSize="12"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <Ellipse Style="{StaticResource StatusIndicatorStyle}"
                                    Fill="{StaticResource WarningBrush}"
                                    Margin="0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 装饰线条 -->
                <Rectangle Grid.Row="0"
                          VerticalAlignment="Bottom"
                          Style="{StaticResource DecorativeLineStyle}"
                          Margin="16,0">
                    <Rectangle.Fill>
                        <SolidColorBrush Color="{StaticResource AccentOrangeColor}"/>
                    </Rectangle.Fill>
                </Rectangle>

                <!-- 内容区域 -->
                <Grid Grid.Row="1" Name="SafetyInterlockArea" Style="{StaticResource ContentAreaStyle}">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <!-- 急停状态 -->
                            <TextBlock Text="急停状态"
                                      Foreground="{StaticResource TextSecondaryBrush}"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Margin="0,0,0,8"/>
                            <UniformGrid Columns="2" Margin="0,0,0,12">
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="急停1" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="急停2" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource ErrorBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="急停3" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="急停4" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </UniformGrid>

                            <!-- 门状态 -->
                            <TextBlock Text="门状态"
                                      Foreground="{StaticResource TextSecondaryBrush}"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Margin="0,8,0,8"/>
                            <UniformGrid Columns="2" Margin="0,0,0,12">
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="1#门" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="2#门" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource WarningBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="3#门" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="4#门" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </UniformGrid>

                            <!-- 声光报警 -->
                            <TextBlock Text="声光报警"
                                      Foreground="{StaticResource TextSecondaryBrush}"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Margin="0,8,0,8"/>
                            <UniformGrid Columns="2">
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource InfoBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="报警1" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="2"
                                       Padding="8,4">
                                    <StackPanel Orientation="Horizontal">
                                        <Ellipse Width="8" Height="8" Fill="{StaticResource InfoBrush}" VerticalAlignment="Center"/>
                                        <TextBlock Text="报警2" Foreground="{StaticResource TextPrimaryBrush}" FontSize="11" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </UniformGrid>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Grid>
        </Border>

        <!-- 中部区域：3D展示和实验进度 -->
        <Border Grid.Column="1"
                Grid.Row="0"
                Grid.RowSpan="2"
                Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}" Height="55">
                    <Grid>
                        <TextBlock Text="电磁辐射实验系统" Style="{StaticResource MainTitleStyle}"/>
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Margin="0,0,20,0">
                            <Border Background="{StaticResource SuccessBrush}"
                                   CornerRadius="12"
                                   Padding="12,4">
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="8" Height="8" Fill="{StaticResource TextPrimaryBrush}" VerticalAlignment="Center"/>
                                    <TextBlock Text="准备阶段"
                                               Foreground="{StaticResource TextPrimaryBrush}"
                                               FontSize="14"
                                               FontWeight="SemiBold"
                                               VerticalAlignment="Center"
                                               Margin="8,0,0,0"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 装饰线条 -->
                <Rectangle Grid.Row="0"
                          VerticalAlignment="Bottom"
                          Style="{StaticResource DecorativeLineStyle}"
                          Margin="20,0"/>

                <!-- 实验进度区域 -->
                <Border Grid.Row="1"
                        Background="{StaticResource ContentBackgroundBrush}"
                        CornerRadius="8"
                        Margin="12,8,12,4"
                        BorderThickness="1"
                        BorderBrush="{StaticResource BorderPrimaryBrush}">
                    <Grid Name="ExperimentProgressArea" Margin="16,12">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 进度标题 -->
                        <TextBlock Grid.Row="0"
                                  Text="实验进度"
                                  Foreground="{StaticResource TextSecondaryBrush}"
                                  FontSize="16"
                                  FontWeight="SemiBold"
                                  Margin="0,0,0,8"/>

                        <!-- 进度条 -->
                        <ProgressBar Grid.Row="1"
                                    Style="{StaticResource ModernProgressBarStyle}"
                                    Value="25"
                                    Maximum="100"
                                    Margin="0,0,0,8"/>

                        <!-- 阶段指示器 -->
                        <UniformGrid Grid.Row="2" Columns="4" Margin="0,4,0,0">
                            <Border Background="{StaticResource SuccessBrush}"
                                   CornerRadius="4"
                                   Margin="2"
                                   Padding="8,4">
                                <TextBlock Text="准备"
                                          Foreground="{StaticResource TextPrimaryBrush}"
                                          FontSize="12"
                                          FontWeight="SemiBold"
                                          HorizontalAlignment="Center"/>
                            </Border>
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="4"
                                   Margin="2"
                                   Padding="8,4"
                                   BorderThickness="1"
                                   BorderBrush="{StaticResource BorderSecondaryBrush}">
                                <TextBlock Text="加压"
                                          Foreground="{StaticResource TextMutedBrush}"
                                          FontSize="12"
                                          HorizontalAlignment="Center"/>
                            </Border>
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="4"
                                   Margin="2"
                                   Padding="8,4"
                                   BorderThickness="1"
                                   BorderBrush="{StaticResource BorderSecondaryBrush}">
                                <TextBlock Text="出光"
                                          Foreground="{StaticResource TextMutedBrush}"
                                          FontSize="12"
                                          HorizontalAlignment="Center"/>
                            </Border>
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="4"
                                   Margin="2"
                                   Padding="8,4"
                                   BorderThickness="1"
                                   BorderBrush="{StaticResource BorderSecondaryBrush}">
                                <TextBlock Text="结论"
                                          Foreground="{StaticResource TextMutedBrush}"
                                          FontSize="12"
                                          HorizontalAlignment="Center"/>
                            </Border>
                        </UniformGrid>
                    </Grid>
                </Border>

                <!-- 3D展示区域 -->
                <Grid Grid.Row="2" Name="Video3DArea" Margin="12,4,12,12">
                    <Border Background="{StaticResource ContentBackgroundBrush}"
                            CornerRadius="8"
                            BorderBrush="{StaticResource BorderSecondaryBrush}"
                            BorderThickness="1">
                        <Border.Effect>
                            <DropShadowEffect Color="{StaticResource ShadowColor}"
                                            Direction="315"
                                            ShadowDepth="4"
                                            Opacity="0.3"
                                            BlurRadius="8"/>
                        </Border.Effect>
                        <Grid>
                            <!-- 视频播放区域占位符 -->
                            <TextBlock Text="3D展示视频区域"
                                       Foreground="{StaticResource TextMutedBrush}"
                                       FontSize="20"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"/>

                            <!-- 播放控制栏 -->
                            <Border VerticalAlignment="Bottom"
                                   Background="#AA000000"
                                   Height="40"
                                   CornerRadius="0,0,8,8">
                                <StackPanel Orientation="Horizontal"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center">
                                    <Button Content="▶"
                                           Style="{StaticResource ModernButtonStyle}"
                                           Width="32"
                                           Height="24"
                                           Margin="4"/>
                                    <Button Content="⏸"
                                           Style="{StaticResource ModernButtonStyle}"
                                           Width="32"
                                           Height="24"
                                           Margin="4"/>
                                    <Button Content="⏹"
                                           Style="{StaticResource ModernButtonStyle}"
                                           Width="32"
                                           Height="24"
                                           Margin="4"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- 右上区域：实验内容信息 -->
        <Border Grid.Column="2"
                Grid.Row="0"
                Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                    <Grid>
                        <TextBlock Text="实验信息" Style="{StaticResource HeaderTextStyle}">
                            <TextBlock.Foreground>
                                <SolidColorBrush Color="{StaticResource AccentGreenColor}"/>
                            </TextBlock.Foreground>
                        </TextBlock>
                        <StackPanel Orientation="Horizontal"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"
                                   Margin="0,0,16,0">
                            <TextBlock Text="就绪"
                                      Foreground="{StaticResource SuccessBrush}"
                                      FontSize="12"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <Ellipse Style="{StaticResource StatusIndicatorStyle}"
                                    Fill="{StaticResource SuccessBrush}"
                                    Margin="0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 装饰线条 -->
                <Rectangle Grid.Row="0"
                          VerticalAlignment="Bottom"
                          Style="{StaticResource DecorativeLineStyle}"
                          Margin="16,0">
                    <Rectangle.Fill>
                        <SolidColorBrush Color="{StaticResource AccentGreenColor}"/>
                    </Rectangle.Fill>
                </Rectangle>

                <!-- 内容区域 -->
                <Grid Grid.Row="1" Name="ExperimentInfoArea" Style="{StaticResource ContentAreaStyle}">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <!-- 实验单位 -->
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="6"
                                   Padding="12,8"
                                   Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="实验单位"
                                              Foreground="{StaticResource AccentGreenBrush}"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,4"/>
                                    <TextBlock Text="电磁辐射实验室"
                                              Foreground="{StaticResource TextPrimaryBrush}"
                                              FontSize="14"
                                              FontWeight="SemiBold"/>
                                </StackPanel>
                            </Border>

                            <!-- 实验编号 -->
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="6"
                                   Padding="12,8"
                                   Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="实验编号"
                                              Foreground="{StaticResource AccentGreenBrush}"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,4"/>
                                    <TextBlock Text="EXP-2025-001"
                                              Foreground="{StaticResource TextPrimaryBrush}"
                                              FontSize="14"
                                              FontWeight="SemiBold"/>
                                </StackPanel>
                            </Border>

                            <!-- 实验名称 -->
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="6"
                                   Padding="12,8"
                                   Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="实验名称"
                                              Foreground="{StaticResource AccentGreenBrush}"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,4"/>
                                    <TextBlock Text="电磁辐射效应测试"
                                              Foreground="{StaticResource TextPrimaryBrush}"
                                              FontSize="14"
                                              FontWeight="SemiBold"
                                              TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- 实验目的 -->
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="6"
                                   Padding="12,8"
                                   Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="实验目的"
                                              Foreground="{StaticResource AccentGreenBrush}"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,4"/>
                                    <TextBlock Text="测试设备在电磁辐射环境下的性能表现"
                                              Foreground="{StaticResource TextSecondaryBrush}"
                                              FontSize="12"
                                              TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>

                            <!-- 预计时长 -->
                            <Border Background="{StaticResource ContentBackgroundBrush}"
                                   CornerRadius="6"
                                   Padding="12,8"
                                   Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="预计时长"
                                              Foreground="{StaticResource AccentGreenBrush}"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              Margin="0,0,0,4"/>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="120"
                                                  Foreground="{StaticResource AccentYellowBrush}"
                                                  FontSize="16"
                                                  FontWeight="Bold"/>
                                        <TextBlock Text=" 分钟"
                                                  Foreground="{StaticResource TextSecondaryBrush}"
                                                  FontSize="12"
                                                  VerticalAlignment="Bottom"
                                                  Margin="2,0,0,2"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- 当前状态 -->
                            <Border Background="{StaticResource SuccessBrush}"
                                   CornerRadius="6"
                                   Padding="12,8">
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="12" Height="12" Fill="{StaticResource TextPrimaryBrush}" VerticalAlignment="Center"/>
                                    <TextBlock Text="系统就绪，等待开始实验"
                                              Foreground="{StaticResource TextPrimaryBrush}"
                                              FontSize="12"
                                              FontWeight="SemiBold"
                                              VerticalAlignment="Center"
                                              Margin="8,0,0,0"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Grid>
        </Border>

        <!-- 右下区域：装置状态 -->
        <Border Grid.Column="2"
                Grid.Row="1"
                Style="{StaticResource ModernCardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Border Grid.Row="0" Style="{StaticResource ModernHeaderStyle}">
                    <Grid>
                        <TextBlock Text="装置状态" Style="{StaticResource HeaderTextStyle}">
                            <TextBlock.Foreground>
                                <SolidColorBrush Color="{StaticResource AccentRedColor}"/>
                            </TextBlock.Foreground>
                        </TextBlock>
                        <StackPanel Orientation="Horizontal"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"
                                   Margin="0,0,16,0">
                            <TextBlock Text="运行中"
                                      Foreground="{StaticResource SuccessBrush}"
                                      FontSize="12"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <Ellipse Style="{StaticResource StatusIndicatorStyle}"
                                    Fill="{StaticResource SuccessBrush}"
                                    Margin="0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 装饰线条 -->
                <Rectangle Grid.Row="0"
                          VerticalAlignment="Bottom"
                          Style="{StaticResource DecorativeLineStyle}"
                          Margin="16,0">
                    <Rectangle.Fill>
                        <SolidColorBrush Color="{StaticResource AccentRedColor}"/>
                    </Rectangle.Fill>
                </Rectangle>

                <!-- 内容区域 -->
                <Grid Grid.Row="1" Name="DeviceStatusArea" Style="{StaticResource ContentAreaStyle}">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <!-- 环境参数 -->
                            <TextBlock Text="环境参数"
                                      Foreground="{StaticResource TextSecondaryBrush}"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Margin="0,0,0,8"/>
                            <UniformGrid Columns="2" Margin="0,0,0,12">
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="6"
                                       Margin="2"
                                       Padding="8">
                                    <StackPanel>
                                        <TextBlock Text="温度"
                                                  Foreground="{StaticResource AccentRedBrush}"
                                                  FontSize="11"
                                                  FontWeight="SemiBold"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <TextBlock Text="23.5"
                                                      Foreground="{StaticResource TextPrimaryBrush}"
                                                      FontSize="16"
                                                      FontWeight="Bold"/>
                                            <TextBlock Text="°C"
                                                      Foreground="{StaticResource TextSecondaryBrush}"
                                                      FontSize="12"
                                                      VerticalAlignment="Bottom"
                                                      Margin="2,0,0,2"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="6"
                                       Margin="2"
                                       Padding="8">
                                    <StackPanel>
                                        <TextBlock Text="湿度"
                                                  Foreground="{StaticResource AccentRedBrush}"
                                                  FontSize="11"
                                                  FontWeight="SemiBold"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <TextBlock Text="45"
                                                      Foreground="{StaticResource TextPrimaryBrush}"
                                                      FontSize="16"
                                                      FontWeight="Bold"/>
                                            <TextBlock Text="%"
                                                      Foreground="{StaticResource TextSecondaryBrush}"
                                                      FontSize="12"
                                                      VerticalAlignment="Bottom"
                                                      Margin="2,0,0,2"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </UniformGrid>

                            <!-- 系统状态 -->
                            <TextBlock Text="系统状态"
                                      Foreground="{StaticResource TextSecondaryBrush}"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Margin="0,8,0,8"/>
                            <StackPanel>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="0,2"
                                       Padding="8,6">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0"
                                                  Text="数采系统"
                                                  Foreground="{StaticResource TextPrimaryBrush}"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"/>
                                        <Ellipse Grid.Column="1"
                                                Width="8"
                                                Height="8"
                                                Fill="{StaticResource SuccessBrush}"/>
                                    </Grid>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="0,2"
                                       Padding="8,6">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0"
                                                  Text="真空系统"
                                                  Foreground="{StaticResource TextPrimaryBrush}"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"/>
                                        <Ellipse Grid.Column="1"
                                                Width="8"
                                                Height="8"
                                                Fill="{StaticResource SuccessBrush}"/>
                                    </Grid>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="0,2"
                                       Padding="8,6">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0"
                                                  Text="充放电系统"
                                                  Foreground="{StaticResource TextPrimaryBrush}"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"/>
                                        <Ellipse Grid.Column="1"
                                                Width="8"
                                                Height="8"
                                                Fill="{StaticResource WarningBrush}"/>
                                    </Grid>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="0,2"
                                       Padding="8,6">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0"
                                                  Text="触发系统"
                                                  Foreground="{StaticResource TextPrimaryBrush}"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"/>
                                        <Ellipse Grid.Column="1"
                                                Width="8"
                                                Height="8"
                                                Fill="{StaticResource SuccessBrush}"/>
                                    </Grid>
                                </Border>
                                <Border Background="{StaticResource ContentBackgroundBrush}"
                                       CornerRadius="4"
                                       Margin="0,2"
                                       Padding="8,6">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0"
                                                  Text="远程加电系统"
                                                  Foreground="{StaticResource TextPrimaryBrush}"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"/>
                                        <Ellipse Grid.Column="1"
                                                Width="8"
                                                Height="8"
                                                Fill="{StaticResource SuccessBrush}"/>
                                    </Grid>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Grid>
        </Border>

        <!-- 顶部状态栏 -->
        <Border Grid.Column="0"
                Grid.ColumnSpan="3"
                VerticalAlignment="Top"
                Height="32"
                Margin="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#CC000000" Offset="0"/>
                    <GradientStop Color="#AA000000" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <Ellipse Width="8" Height="8" Fill="{StaticResource AccentCyanBrush}" VerticalAlignment="Center"/>
                    <TextBlock Text="电磁辐射实验大屏展示系统 v1.0"
                               Foreground="{StaticResource TextSecondaryBrush}"
                               FontSize="12"
                               FontWeight="SemiBold"
                               VerticalAlignment="Center"
                               Margin="8,0,0,0"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Margin="0,0,20,0">
                    <Border Background="{StaticResource SuccessBrush}"
                           CornerRadius="8"
                           Padding="8,2"
                           Margin="0,0,12,0">
                        <TextBlock Text="系统正常"
                                  Foreground="{StaticResource TextPrimaryBrush}"
                                  FontSize="10"
                                  FontWeight="SemiBold"/>
                    </Border>
                    <TextBlock Name="SystemTimeDisplay"
                               Text="2025-01-16 14:30:00"
                               Foreground="{StaticResource AccentCyanBrush}"
                               FontSize="12"
                               FontWeight="SemiBold"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
