using System;
using System.ComponentModel;

namespace MainDispaly.Models
{
    /// <summary>
    /// 实验数据模型
    /// </summary>
    public class ExperimentData : INotifyPropertyChanged
    {
        private string _experimentUnit;
        private string _experimentNumber;
        private string _experimentName;
        private string _experimentPurpose;
        private int _estimatedDuration;
        private ExperimentStatus _status;
        private DateTime _startTime;
        private DateTime _endTime;
        private string _statusMessage;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 实验单位
        /// </summary>
        public string ExperimentUnit
        {
            get => _experimentUnit;
            set
            {
                _experimentUnit = value;
                OnPropertyChanged(nameof(ExperimentUnit));
            }
        }

        /// <summary>
        /// 实验编号
        /// </summary>
        public string ExperimentNumber
        {
            get => _experimentNumber;
            set
            {
                _experimentNumber = value;
                OnPropertyChanged(nameof(ExperimentNumber));
            }
        }

        /// <summary>
        /// 实验名称
        /// </summary>
        public string ExperimentName
        {
            get => _experimentName;
            set
            {
                _experimentName = value;
                OnPropertyChanged(nameof(ExperimentName));
            }
        }

        /// <summary>
        /// 实验目的
        /// </summary>
        public string ExperimentPurpose
        {
            get => _experimentPurpose;
            set
            {
                _experimentPurpose = value;
                OnPropertyChanged(nameof(ExperimentPurpose));
            }
        }

        /// <summary>
        /// 预计时长（分钟）
        /// </summary>
        public int EstimatedDuration
        {
            get => _estimatedDuration;
            set
            {
                _estimatedDuration = value;
                OnPropertyChanged(nameof(EstimatedDuration));
            }
        }

        /// <summary>
        /// 实验状态
        /// </summary>
        public ExperimentStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
                OnPropertyChanged(nameof(StatusText));
            }
        }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText
        {
            get
            {
                return Status switch
                {
                    ExperimentStatus.Ready => "系统就绪，等待开始实验",
                    ExperimentStatus.Running => "实验进行中",
                    ExperimentStatus.Paused => "实验已暂停",
                    ExperimentStatus.Completed => "实验已完成",
                    ExperimentStatus.Error => "实验出现错误",
                    _ => "未知状态"
                };
            }
        }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime
        {
            get => _startTime;
            set
            {
                _startTime = value;
                OnPropertyChanged(nameof(StartTime));
            }
        }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime
        {
            get => _endTime;
            set
            {
                _endTime = value;
                OnPropertyChanged(nameof(EndTime));
            }
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        /// <summary>
        /// 实验持续时间
        /// </summary>
        public TimeSpan Duration => EndTime > StartTime ? EndTime - StartTime : TimeSpan.Zero;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ExperimentData()
        {
            Status = ExperimentStatus.Ready;
            ExperimentUnit = "电磁辐射实验室";
            ExperimentNumber = "EXP-2025-001";
            ExperimentName = "电磁辐射效应测试";
            ExperimentPurpose = "测试设备在电磁辐射环境下的性能表现";
            EstimatedDuration = 120;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 实验状态枚举
    /// </summary>
    public enum ExperimentStatus
    {
        Ready,      // 就绪
        Running,    // 运行中
        Paused,     // 暂停
        Completed,  // 完成
        Error       // 错误
    }
}
