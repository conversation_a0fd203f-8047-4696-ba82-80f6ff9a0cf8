using System;
using System.ComponentModel;

namespace MainDispaly.Models
{
    /// <summary>
    /// 视频源模型
    /// </summary>
    public class VideoSource : INotifyPropertyChanged
    {
        private string _name;
        private string _sourceUrl;
        private VideoSourceType _sourceType;
        private VideoStatus _status;
        private string _statusMessage;
        private DateTime _lastUpdateTime;
        private int _width;
        private int _height;
        private double _frameRate;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 视频源名称
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        /// <summary>
        /// 视频源URL或路径
        /// </summary>
        public string SourceUrl
        {
            get => _sourceUrl;
            set
            {
                _sourceUrl = value;
                OnPropertyChanged(nameof(SourceUrl));
            }
        }

        /// <summary>
        /// 视频源类型
        /// </summary>
        public VideoSourceType SourceType
        {
            get => _sourceType;
            set
            {
                _sourceType = value;
                OnPropertyChanged(nameof(SourceType));
            }
        }

        /// <summary>
        /// 视频状态
        /// </summary>
        public VideoStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
                OnPropertyChanged(nameof(StatusText));
            }
        }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText
        {
            get
            {
                return Status switch
                {
                    VideoStatus.Disconnected => "未连接",
                    VideoStatus.Connecting => "连接中",
                    VideoStatus.Connected => "已连接",
                    VideoStatus.Playing => "播放中",
                    VideoStatus.Paused => "已暂停",
                    VideoStatus.Error => "错误",
                    _ => "未知状态"
                };
            }
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged(nameof(StatusMessage));
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                _lastUpdateTime = value;
                OnPropertyChanged(nameof(LastUpdateTime));
            }
        }

        /// <summary>
        /// 视频宽度
        /// </summary>
        public int Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged(nameof(Width));
                OnPropertyChanged(nameof(Resolution));
            }
        }

        /// <summary>
        /// 视频高度
        /// </summary>
        public int Height
        {
            get => _height;
            set
            {
                _height = value;
                OnPropertyChanged(nameof(Height));
                OnPropertyChanged(nameof(Resolution));
            }
        }

        /// <summary>
        /// 帧率
        /// </summary>
        public double FrameRate
        {
            get => _frameRate;
            set
            {
                _frameRate = value;
                OnPropertyChanged(nameof(FrameRate));
            }
        }

        /// <summary>
        /// 分辨率字符串
        /// </summary>
        public string Resolution => $"{Width}x{Height}";

        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline => Status == VideoStatus.Connected || Status == VideoStatus.Playing;

        /// <summary>
        /// 构造函数
        /// </summary>
        public VideoSource()
        {
            Status = VideoStatus.Disconnected;
            LastUpdateTime = DateTime.Now;
            Width = 1920;
            Height = 1080;
            FrameRate = 30.0;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">名称</param>
        /// <param name="sourceUrl">源URL</param>
        /// <param name="sourceType">源类型</param>
        public VideoSource(string name, string sourceUrl, VideoSourceType sourceType) : this()
        {
            Name = name;
            SourceUrl = sourceUrl;
            SourceType = sourceType;
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="status">新状态</param>
        /// <param name="message">状态消息</param>
        public void UpdateStatus(VideoStatus status, string message = null)
        {
            Status = status;
            StatusMessage = message ?? StatusText;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新视频信息
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="frameRate">帧率</param>
        public void UpdateVideoInfo(int width, int height, double frameRate)
        {
            Width = width;
            Height = height;
            FrameRate = frameRate;
            LastUpdateTime = DateTime.Now;
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 视频源类型枚举
    /// </summary>
    public enum VideoSourceType
    {
        Camera,     // 摄像头
        File,       // 文件
        Stream,     // 流媒体
        Network     // 网络摄像头
    }

    /// <summary>
    /// 视频状态枚举
    /// </summary>
    public enum VideoStatus
    {
        Disconnected,   // 未连接
        Connecting,     // 连接中
        Connected,      // 已连接
        Playing,        // 播放中
        Paused,         // 已暂停
        Error           // 错误
    }
}
