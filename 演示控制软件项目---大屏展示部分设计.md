# 演示控制软件项目---大屏展示部分设计

---

## 一、项目背景

​	为解决电磁辐射实验过程中，实验人员及参观人员无法知悉实验进度、实验过程、设备内部情况及实验结束后无法及时获取到实验结果的痛点，故提出使用大屏展示的方式展示实验的各个阶段，其中包括设备内部实验过程3D视频、加压过程、实验过程中生成的各种波形数据、对实验场地的视频监控部分、视频场地的安全联锁装备的控制、实验设备状态的读取、各个子系统状态数据的读取。从而实现数据概览，实验速报、对实验掌控等功能，提高对实验的掌控及效率。

## 二、实验过程

>​	 实验总体过程分为准备阶段、加压阶段、出光阶段、速报阶段共四个阶段，每个阶段需要显示的波形数据与内容不同，需要根据实验过程的重点来确定需要显示的内容。

### 	1. 准备阶段

​	在准备的实验阶段，需要展示设备的全貌，此时需要3D视频对设备进行简短介绍，系统初始化、安全联锁检查、设备状态确认、实验内容介绍、实验监控。

### 	2. 加压阶段

​	加压阶段高压电容充电、电容储能、效应区域测量、监视并控制安全联锁状态、效应区的监控摄像头画面的读取、装置控制信息的读取。

### 			3. 出光阶段

​	在出光阶段，触发信号给到充满电的电容组，电容组受到触发开始放电，设备出光到效应区，效应区的测量探头开始测量信号量，系统读取装置各系统状态、并实时监控安全联锁系统的状态和效应区的摄像头画面，效应区测量软件开始测量效应区波形。

### 	4. 结论阶段

​	在结论阶段，系统测量场、电压、电流、辐射等参数，并进行处理，在大屏上进行显示，系统根据实验数据自动生成实验速报。

## 三、大屏页面设计

>**总体设计准则：**
>
>​	系统大屏页面设计根据实验阶段分为准备、加压、出光、结论四个阶段，每个阶段可以通过手动或者读取设备状态根据实验具体阶段自动进行大屏显示阶段的切换，大屏界面每个阶段根据实验流程的不同阶段侧重显示内容不同，需要根据具体实验的数据侧重点来展示具体实验相关的模块。
>
> 	在大屏界面的显示区域可以手动进行大屏各个区域的显示内容切换，以便于用户可以自主调整页面布局，操作方式可以为拖拽或点选（下拉框）。
>
>​	页面总体布局为横向左中右三个部分，纵向布局为上下两个部分，其中左列与右列等宽且为中列的1/2宽度，左列等分为上下两个部分，中列不进行分割，右列同左列分为上下两个部分，总体上是三纵两横五布局页面。参考如下图：
>![image-20250613161401696](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613161401696.png)

### 	1. 准备阶段

- 页面左上角：

    - 显示视频监控的播放页面模块。

    - 此页面可为多路播放，具体的播放路数可以在文件中进行配置。

    - 需要播放的视频地址可以在配置文件中进行指定。

    - 参考：

        <img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613162906604.png" alt="image-20250613162906604" style="zoom:50%;" />

- 页面左下角：

    - 显示安全联锁状态
    - 此页面可以控制安全联锁装置，实现内容有：安全联锁装置状态读取、写入。
    - 安全联锁装置的种类包括：
        - 急停：
            - 操作类型：
                - 急停1读取/控制（写入）
                - 急停2读取/控制（写入）
                - 急停3读取/控制（写入）
                - 急停4读取/控制（写入）
        - 门关到位状态：
            - 操作类型：
                - 1#门状态读取
                - 2#门状态读取
                - 3#门状态读取
                - 4#门状态读取
        - 远程控制门状态：
            - 操作类型：
                - 1#门远程开门
                - 1#门远程关门
                - 1#门远程强控
                - 2#门远程开门
                - 2#门远程关门
                - 2#门远程强控
                - 3# 门禁释放（允许现场开门）
                - 3# 门禁锁定（禁止现场开门）
                - 4#门禁释放（允许现场开门）
                - 4#门禁锁定（禁止现场开门）
        - 装置终止：
            - 操作类型
                - 装置终止状态写入
        - 声光报警：
            - 操作类型：
                - 声光报警1状态读取
                - 声光报警2状态读取
                - 声光报警3状态读取
                - 声光报警4状态读取
    - 参考：![image-20250613165933403](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613165933403.png)
    - ![image-20250613165957906](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613165957906.png)

- 页面中部：

    - 页面中部下方区域，播放准备阶段的3D展示视频，视频地址可以在配置文件中进行配置。

    - 中部上方显示实验进度，进度图、当前实验的阶段、当前阶段的主要内容、成果等信息，把页面丰富起来。

    - 显示内容参考：

        ![image-20250613170420781](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613170420781.png)

- 页面右上角区域：

    - 显示本次实验的实验内容，内容包括：

        - 实验单位：xxx实验室

        - 实验简介：实验编号、实验名称、实验目的、预计实验时长

        - 实验各阶段描述：准备阶段、加压阶段、开始阶段、结论阶段

        - 参考：

            ![image-20250613170909461](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613170909461.png)

- 页面右下角区域：

    - 装置状态界面：
        - 环境温度、湿度
        - 数采系统状态、真空系统状态、充放电系统状态、换气系统状态、磁芯复位系统状态、触发系统状态、设备远程加电系统状态等各系统状态
        - 参考如下图：
            - ![image-20250613171146167](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613171146167.png)

### 	2 加压阶段，可以先空着后续再定义

### 3. 出光阶段，可以先空着后续再定义

### 4. 结论阶段，可以先控制后续再定义

### 现页面问题：

 	1. 太过于扁平化，没有层次 只有线面等，装饰性较少
 	2. 布局不合理，页面空间位置利用不佳，拥挤的地方太过于拥挤，稀疏的地方太过于稀疏
 	3. 页面不美观不够现代化，
 	4. 色彩太过于单一，整个页面除了蓝色还是蓝色
 	5. 可参考以下：
     	1. ![image-20250613171839042](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613171839042.png)
     	2. ![image-20250613171859872](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613171859872.png)
     	3. ![image-20250613171927246](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613171927246.png)

![image-20250613171949205](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250613171949205.png)