using System;
using System.Windows;
using System.Windows.Threading;

namespace MainDispaly
{
    /// <summary>
    /// TestWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TestWindow : Window
    {
        private DispatcherTimer timer;

        public TestWindow()
        {
            InitializeComponent();
            InitializeTimer();
            InitializeWindow();
        }

        /// <summary>
        /// 初始化时间显示定时器
        /// </summary>
        private void InitializeTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
            timer.Start();
        }

        /// <summary>
        /// 定时器事件，更新系统时间显示
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            if (SystemTimeDisplay != null)
            {
                SystemTimeDisplay.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        /// <summary>
        /// 初始化窗口设置
        /// </summary>
        private void InitializeWindow()
        {
            // 设置窗口全屏
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;

            // 添加键盘事件处理
            this.KeyDown += TestWindow_KeyDown;
        }

        /// <summary>
        /// 键盘事件处理
        /// </summary>
        private void TestWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                // ESC键切换窗口模式
                if (this.WindowState == WindowState.Maximized && this.WindowStyle == WindowStyle.None)
                {
                    this.WindowStyle = WindowStyle.SingleBorderWindow;
                    this.WindowState = WindowState.Normal;
                }
                else
                {
                    this.WindowStyle = WindowStyle.None;
                    this.WindowState = WindowState.Maximized;
                }
            }
            else if (e.Key == System.Windows.Input.Key.F11)
            {
                // F11键全屏切换
                if (this.WindowState == WindowState.Maximized)
                {
                    this.WindowState = WindowState.Normal;
                }
                else
                {
                    this.WindowState = WindowState.Maximized;
                }
            }
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            timer?.Stop();
            base.OnClosed(e);
        }
    }
}